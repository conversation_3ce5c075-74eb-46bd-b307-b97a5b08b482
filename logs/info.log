2025-09-18 14:19:02.371 [36mINFO [m [35m[main][m [36mc.a.n.c.e.SearchableProperties[m [34m[][m - [32mproperties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[m2025-09-18 14:19:02.437 [36mINFO [m [35m[background-preinit][m [36mo.h.v.i.u.Version[m [34m[][m - [32mHV000001: Hibernate Validator 6.2.5.Final
[m2025-09-18 14:19:05.346 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 14:19:05.346 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 14:19:09.241 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-18 14:19:09.250 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-18 14:19:09.256 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-18 14:19:09.258 [36mINFO [m [35m[main][m [36mo.s.c.b.c.PropertySourceBootstrapConfiguration[m [34m[][m - [32mLocated property source: [BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive-druid.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive,default'}]
[m2025-09-18 14:19:09.305 [36mINFO [m [35m[main][m [36mc.h.AppMain[m [34m[][m - [32mThe following 1 profile is active: "druid"
[m2025-09-18 14:19:12.666 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 14:19:12.678 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 14:19:12.784 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 74 ms. Found 0 Elasticsearch repository interfaces.
[m2025-09-18 14:19:12.792 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 14:19:12.795 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 14:19:12.834 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 39 ms. Found 0 Reactive Elasticsearch repository interfaces.
[m2025-09-18 14:19:12.858 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 14:19:12.862 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Redis repositories in DEFAULT mode.
[m2025-09-18 14:19:12.918 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 40 ms. Found 0 Redis repository interfaces.
[m2025-09-18 14:19:13.624 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.624 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.625 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.625 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.625 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.625 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.626 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.626 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.626 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:13.626 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:19:14.652 [36mINFO [m [35m[main][m [36mo.s.c.c.s.GenericScope[m [34m[][m - [32mBeanFactory id=f26d4248-e3fc-381f-8478-184d97d56f2e
[m2025-09-18 14:19:16.334 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:19:16.345 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:19:16.348 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$597/258548151] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:19:16.365 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:19:16.694 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'dictProperties' of type [com.hl.dict.config.DictProperties$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:19:16.702 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'dynamicTableNameHandler' of type [com.hl.dict.config.DynamicTableNameHandler$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:19:18.036 [36mINFO [m [35m[main][m [36mo.s.b.w.e.t.TomcatWebServer[m [34m[][m - [32mTomcat initialized with port(s): 28183 (http)
[m2025-09-18 14:19:18.117 [36mINFO [m [35m[main][m [36mo.a.c.h.Http11NioProtocol[m [34m[][m - [32mInitializing ProtocolHandler ["http-nio-28183"]
[m2025-09-18 14:19:18.124 [36mINFO [m [35m[main][m [36mo.a.c.c.StandardService[m [34m[][m - [32mStarting service [Tomcat]
[m2025-09-18 14:19:18.124 [36mINFO [m [35m[main][m [36mo.a.c.c.StandardEngine[m [34m[][m - [32mStarting Servlet engine: [Apache Tomcat/9.0.83]
[m2025-09-18 14:19:18.567 [36mINFO [m [35m[main][m [36mo.a.c.c.C.[.[.[/][m [34m[][m - [32mInitializing Spring embedded WebApplicationContext
[m2025-09-18 14:19:18.567 [36mINFO [m [35m[main][m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[m [34m[][m - [32mRoot WebApplicationContext: initialization completed in 9233 ms
[m2025-09-18 14:19:19.128 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'sso-hl' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 14:19:20.213 [36mINFO [m [35m[main][m [36mc.h.d.c.MybatisPlusEnhancerConfiguration[m [34m[][m - [32m✅ Enhanced MybatisPlusInterceptor with DynamicTableNameInnerInterceptor.
[m2025-09-18 14:19:24.486 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-18 14:19:24.487 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.505 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-18 14:19:24.506 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.523 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-18 14:19:24.523 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.541 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-18 14:19:24.542 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.557 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-18 14:19:24.557 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.574 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-18 14:19:24.575 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.593 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-18 14:19:24.593 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.609 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-18 14:19:24.609 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.625 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-18 14:19:24.626 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.643 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-18 14:19:24.644 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.662 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-18 14:19:24.663 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.681 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-18 14:19:24.682 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.700 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-18 14:19:24.736 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-18 14:19:24.737 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.760 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-18 14:19:24.760 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.778 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-18 14:19:24.779 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.799 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjLzfx".
[m2025-09-18 14:19:24.800 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjLzfx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.816 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-18 14:19:24.817 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.836 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-18 14:19:24.836 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.856 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-18 14:19:24.857 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.878 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-18 14:19:24.879 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.898 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-18 14:19:24.898 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.915 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-18 14:19:24.916 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.932 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-18 14:19:24.932 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:24.948 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-18 14:19:24.949 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.001 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-18 14:19:25.001 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.029 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-18 14:19:25.029 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.046 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-18 14:19:25.046 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.062 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-18 14:19:25.062 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.078 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-18 14:19:25.080 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.095 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-18 14:19:25.095 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.114 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-18 14:19:25.114 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.130 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-18 14:19:25.130 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.145 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-18 14:19:25.146 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.164 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-18 14:19:25.164 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.180 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-18 14:19:25.181 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.197 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-18 14:19:25.198 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.216 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-18 14:19:25.216 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.228 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-18 14:19:25.230 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.257 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-18 14:19:25.257 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.280 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-18 14:19:25.281 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.297 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-18 14:19:25.297 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.316 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-18 14:19:25.316 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.335 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-18 14:19:25.336 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.354 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-18 14:19:25.355 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.372 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-18 14:19:25.372 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.389 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-18 14:19:25.389 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.406 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-18 14:19:25.406 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.428 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjYjbb".
[m2025-09-18 14:19:25.428 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjYjbb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.446 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-18 14:19:25.447 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.475 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-18 14:19:25.475 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.507 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-18 14:19:25.508 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.539 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-18 14:19:25.539 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.559 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-18 14:19:25.559 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:25.578 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-18 14:19:25.578 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:19:26.724 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'hl-task' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 14:19:43.283 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:19:46.845 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:19:46.846 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:19:46.966 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:19:50.001 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:19:50.002 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:19:50.144 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:19:53.179 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:19:53.179 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:19:53.316 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:19:56.365 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:19:56.365 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:19:56.508 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:19:59.542 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:19:59.542 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:19:59.683 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:20:02.719 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:20:02.719 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:20:02.866 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:20:05.905 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:20:05.906 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:20:06.042 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:20:09.261 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:20:09.262 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:20:09.762 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CG9Z9HJ3FJW -> BusinessTripStrategy
[m2025-09-18 14:20:09.762 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: C09ZZEL29S8 -> LeaveChangZhouStrategy
[m2025-09-18 14:20:09.762 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CC42ITDEHRQ -> LeaveRequestStrategy
[m2025-09-18 14:20:09.763 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CKSWWQQWW7H -> OverseasTravelStrategy
[m2025-09-18 14:20:09.763 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CNE2TZD2GTE -> PoliceAbilityStrategy
[m2025-09-18 14:20:09.763 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CXQNJMIAR1C -> PoliceClubActivityStrategy
[m2025-09-18 14:20:09.763 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: C30NIBEJEGI -> PoliceDrinkReportStrategy
[m2025-09-18 14:20:09.978 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:20:13.021 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:20:13.021 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:20:13.400 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:20:16.428 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:20:16.430 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:20:34.914 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 14:20:35.263 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 14:20:35.264 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 14:20:36.384 [36mINFO [m [35m[main][m [36mc.h.n.c.SharedNacosNameSpace[m [34m[][m - [32m{"secretKey":"","password":"hl123","namespace":"public","accessKey":"","serverAddr":"**************:8848","isUseCloudNamespaceParsing":"false","clusterName":"","username":"nacos"}
[m2025-09-18 14:20:37.625 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 14:20:38.809 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'hl-dict' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 14:20:44.793 [36mINFO [m [35m[main][m [36mo.s.b.a.e.w.EndpointLinksResolver[m [34m[][m - [32mExposing 1 endpoint(s) beneath base path '/actuator'
[m2025-09-18 14:20:46.624 [36mINFO [m [35m[main][m [36mc.h.s.c.SecurityConfig[m [34m[][m - [32m[/error/logs, /test/dict, /dict/api-query, /dict-test/test, /dict/add-batch] --> 200
[m2025-09-18 14:20:47.049 [36mINFO [m [35m[main][m [36mo.s.s.w.DefaultSecurityFilterChain[m [34m[][m - [32mWill secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@61b121e4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@76d7ec52, org.springframework.security.web.context.SecurityContextPersistenceFilter@46b27d1, org.springframework.security.web.header.HeaderWriterFilter@7fe3cd91, org.springframework.security.web.authentication.logout.LogoutFilter@67e206c5, org.springframework.web.filter.CorsFilter@518990a6, com.hl.security.config.sso.SsoAuthTokenFilter@2c5dfc9b, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@66881001, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5e59113f, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4b4ac58d, org.springframework.security.web.session.SessionManagementFilter@574b1fb6, org.springframework.security.web.access.ExceptionTranslationFilter@65e1e974, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4b8bc280]
[m2025-09-18 14:20:54.982 [36mINFO [m [35m[main][m [36mo.a.c.h.Http11NioProtocol[m [34m[][m - [32mStarting ProtocolHandler ["http-nio-28183"]
[m2025-09-18 14:20:55.137 [36mINFO [m [35m[main][m [36mo.s.b.w.e.t.TomcatWebServer[m [34m[][m - [32mTomcat started on port(s): 28183 (http) with context path ''
[m2025-09-18 14:20:56.358 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 14:20:56.369 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 14:20:56.369 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 14:20:56.635 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mnacos registry, default hl-wj-police-archive *************:28183 register finished
[m2025-09-18 14:21:01.178 [36mINFO [m [35m[main][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 14:21:01.797 [36mINFO [m [35m[main][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mCreated new connection: rabbitConnectionFactory#2ce9f29c:0/SimpleConnection@30fa5713 [delegate=amqp://admin@**************:5672/, localPort= 58200]
[m2025-09-18 14:21:02.499 [36mINFO [m [35m[main][m [36mc.h.AppMain[m [34m[][m - [32mStarted AppMain in 122.242 seconds (JVM running for 132.642)
[m2025-09-18 14:21:02.501 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 14:21:02.502 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 14:21:07.086 [36mINFO [m [35m[main][m [36mc.h.s.c.s.c.SsoCache[m [34m[][m - [32minit cache_data -> size 21 --> ["user_circle","role","org_job_user","user_resources","project","resources","user_leave","resources_user","user_all","user_role_meet","circle_user","user_role","role_resources","police","role_user","organization","user_org_role","job","circle","user","organization_tree"] -> 2025-09-18 09:44:25
[m2025-09-18 14:21:07.091 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 14:21:07.091 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 14:21:07.399 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive.properties, group=default
[m2025-09-18 14:21:07.400 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive-druid.properties, group=default
[m2025-09-18 14:21:07.400 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive, group=default
[m2025-09-18 14:21:07.472 [36mINFO [m [35m[main][m [36mc.h.d.s.i.DictDataServiceImpl[m [34m[][m - [32mrefresh dict cache 
[m2025-09-18 14:21:08.551 [36mINFO [m [35m[RMI TCP Connection(9)-*************][m [36mo.a.c.c.C.[.[.[/][m [34m[][m - [32mInitializing Spring DispatcherServlet 'dispatcherServlet'
[m2025-09-18 14:21:08.552 [36mINFO [m [35m[RMI TCP Connection(9)-*************][m [36mo.s.w.s.DispatcherServlet[m [34m[][m - [32mInitializing Servlet 'dispatcherServlet'
[m2025-09-18 14:21:08.631 [36mINFO [m [35m[RMI TCP Connection(9)-*************][m [36mo.s.w.s.DispatcherServlet[m [34m[][m - [32mCompleted initialization in 79 ms
[m2025-09-18 14:21:08.860 [36mINFO [m [35m[main][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：SELECT id,dict_type,parent_id,dict_value,dict_name,sort,field_class,remark,create_user,create_time,update_user,update_time,is_disable,tenant_id,is_system,is_default,extend FROM dict_data
[m2025-09-18 14:21:09.502 [33mWARN [m [35m[RMI TCP Connection(8)-*************][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor144.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-18 14:21:15.576 [31mERROR[m [35m[main][m [36mc.a.d.f.s.StatFilter[m [34m[][m - [31mslow sql 2121 millis. SELECT 1[]
[m2025-09-18 14:21:19.099 [36mINFO [m [35m[main][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} inited
[m2025-09-18 14:21:23.593 [36mINFO [m [35m[RMI TCP Connection(8)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} inited
[m2025-09-18 14:21:27.904 [36mINFO [m [35m[RMI TCP Connection(8)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} inited
[m2025-09-18 14:21:33.331 [36mINFO [m [35m[RMI TCP Connection(8)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} inited
[m2025-09-18 14:24:20.827 [33mWARN [m [35m[AMQP Connection **************:5672][m [36mc.r.c.i.ForgivingExceptionHandler[m [34m[][m - [33mAn unexpected connection driver error occurred (Exception message: Connection reset)
[m2025-09-18 14:24:21.462 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-1][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mRestarting Consumer@3886be2b: tags=[[amq.ctag-td1NuO-YWfa_gZKggCYk3w]], channel=Cached Rabbit Channel: AMQChannel(amqp://admin@**************:5672/,1), conn: Proxy@49f50482 Shared Rabbit Connection: SimpleConnection@30fa5713 [delegate=amqp://admin@**************:5672/, localPort= 58200], acknowledgeMode=MANUAL local queue size=0
[m2025-09-18 14:24:21.475 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 14:24:47.633 [33mWARN [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [33mConsumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
[m2025-09-18 14:24:47.633 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mRestarting Consumer@8330cc0: tags=[[]], channel=null, acknowledgeMode=MANUAL local queue size=0
[m2025-09-18 14:24:47.639 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 14:25:13.762 [33mWARN [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [33mConsumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
[m2025-09-18 14:25:13.762 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-3][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mRestarting Consumer@48139078: tags=[[]], channel=null, acknowledgeMode=MANUAL local queue size=0
[m2025-09-18 14:25:13.769 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-4][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 14:25:39.860 [33mWARN [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-4][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [33mConsumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
[m2025-09-18 14:25:39.860 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-4][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mRestarting Consumer@5f7a3049: tags=[[]], channel=null, acknowledgeMode=MANUAL local queue size=0
[m2025-09-18 14:25:39.873 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-5][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 14:26:06.020 [33mWARN [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-5][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [33mConsumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
[m2025-09-18 14:26:06.020 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-5][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mRestarting Consumer@1e797782: tags=[[]], channel=null, acknowledgeMode=MANUAL local queue size=0
[m2025-09-18 14:26:06.025 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-6][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 14:26:32.150 [33mWARN [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-6][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [33mConsumer raised exception, processing can restart if the connection factory supports it. Exception summary: org.springframework.amqp.AmqpConnectException: java.net.ConnectException: Connection timed out: connect
[m2025-09-18 14:26:32.151 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-6][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mRestarting Consumer@3aea606: tags=[[]], channel=null, acknowledgeMode=MANUAL local queue size=0
[m2025-09-18 14:26:32.173 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-7][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 14:26:35.194 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-7][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mCreated new connection: rabbitConnectionFactory#2ce9f29c:6/SimpleConnection@5790e32d [delegate=amqp://admin@**************:5672/, localPort= 57694]
[m2025-09-18 14:33:16.298 [36mINFO [m [35m[http-nio-28183-exec-1][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：SELECT category_name, feature_name, COUNT(*) as count FROM police_capability_eval WHERE is_deleted = 0 AND category_name IS NOT NULL AND category_name != '' AND feature_name IS NOT NULL AND feature_name != '' GROUP BY category_name, feature_name ORDER BY category_name, COUNT(*) DESC
[m2025-09-18 14:33:16.984 [36mINFO [m [35m[http-nio-28183-exec-1][m [36mc.h.l.LogSlf4j[m [34m[][m - [32m127.0.0.1:/capabilityEval/statistics (局长指令) -> {} -> 478ms -> {"count":0,"data":[{"categoryName":"专业打击","categoryTotalCount":911,"featureStatistics":[{"featureCount":317,"featureName":"侦查研判"},{"featureCount":310,"featureName":"审讯突破"},{"featureCount":100,"featureName":"获取线索"},{"featureCount":59,"featureName":"监所安全管理"},{"featureCount":51,"featureName":"检验鉴定"},{"featureCount":47,"featureName":"实战指挥"},{"featureCount":21,"featureName":"现场勘查"},{"featureCount":6,"featureName":"监所协助破案"}]},{"categoryName":"基础防控","categoryTotalCount":618,"featureStatistics":[{"featureCount":208,"featureName":"安全监管"},{"featureCount":205,"featureName":"治安要素管理"},{"featureCount":139,"featureName":"矛盾化解"},{"featureCount":45,"featureName":"社会动员"},{"featureCount":16,"featureName":"人员因私出入境管理"},{"featureCount":5,"featureName":"涉稳事件处置"}]},{"categoryName":"执法监督","categoryTotalCount":581,"featureStatistics":[{"featureCount":363,"featureName":"案件审核（办理）"},{"featureCount":99,"featureName":"信访举报核查"},{"featureCount":85,"featureName":"执法监督指导"},{"featureCount":21,"featureName":"现场督察"},{"feat
[m2025-09-18 14:41:07.237 [33mWARN [m [35m[Thread-26][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-18 14:41:07.237 [33mWARN [m [35m[Thread-33][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-18 14:41:07.238 [33mWARN [m [35m[Thread-33][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-18 14:41:07.245 [33mWARN [m [35m[Thread-26][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-18 14:41:08.824 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-8][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mWaiting for workers to finish.
[m2025-09-18 14:41:08.826 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mo.s.b.w.e.t.GracefulShutdown[m [34m[][m - [32mCommencing graceful shutdown. Waiting for active requests to complete
[m2025-09-18 14:41:08.952 [36mINFO [m [35m[tomcat-shutdown][m [36mo.s.b.w.e.t.GracefulShutdown[m [34m[][m - [32mGraceful shutdown complete
[m2025-09-18 14:41:09.531 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-8][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mSuccessfully waited for workers to finish.
[m2025-09-18 14:41:09.596 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mDe-registering from Nacos Server now...
[m2025-09-18 14:41:09.603 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mDe-registration finished.
[m2025-09-18 14:41:09.921 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.h.c.t.GraceThreadPool[m [34m[][m - [32m==== 优雅关闭线程池 ====
[m2025-09-18 14:41:09.947 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} closing ...
[m2025-09-18 14:41:09.957 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} closed
[m2025-09-18 14:41:09.958 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} closing ...
[m2025-09-18 14:41:09.958 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} closed
[m2025-09-18 14:41:09.959 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} closing ...
[m2025-09-18 14:41:09.960 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} closed
[m2025-09-18 14:41:09.961 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} closing ...
[m2025-09-18 14:41:09.981 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} closed
[m2025-09-18 14:57:12.418 [36mINFO [m [35m[main][m [36mc.a.n.c.e.SearchableProperties[m [34m[][m - [32mproperties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[m2025-09-18 14:57:12.478 [36mINFO [m [35m[background-preinit][m [36mo.h.v.i.u.Version[m [34m[][m - [32mHV000001: Hibernate Validator 6.2.5.Final
[m2025-09-18 14:57:15.280 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 14:57:15.280 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 14:57:18.877 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-18 14:57:18.886 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-18 14:57:18.890 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-18 14:57:18.894 [36mINFO [m [35m[main][m [36mo.s.c.b.c.PropertySourceBootstrapConfiguration[m [34m[][m - [32mLocated property source: [BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive-druid.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive,default'}]
[m2025-09-18 14:57:18.957 [36mINFO [m [35m[main][m [36mc.h.AppMain[m [34m[][m - [32mThe following 1 profile is active: "druid"
[m2025-09-18 14:57:21.963 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 14:57:21.974 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 14:57:22.095 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 90 ms. Found 0 Elasticsearch repository interfaces.
[m2025-09-18 14:57:22.104 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 14:57:22.105 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 14:57:22.145 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 40 ms. Found 0 Reactive Elasticsearch repository interfaces.
[m2025-09-18 14:57:22.165 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 14:57:22.171 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Redis repositories in DEFAULT mode.
[m2025-09-18 14:57:22.222 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 39 ms. Found 0 Redis repository interfaces.
[m2025-09-18 14:57:22.838 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:57:22.838 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:57:22.839 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:57:22.839 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:57:22.839 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:57:22.839 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:57:22.839 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:57:22.839 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:57:22.840 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:57:22.840 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 14:57:23.819 [36mINFO [m [35m[main][m [36mo.s.c.c.s.GenericScope[m [34m[][m - [32mBeanFactory id=6a689e8b-b3c8-374c-807c-3d8ec15b808c
[m2025-09-18 14:57:25.392 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:57:25.403 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:57:25.408 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$597/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:57:25.424 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:57:25.718 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'dictProperties' of type [com.hl.dict.config.DictProperties$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:57:25.726 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'dynamicTableNameHandler' of type [com.hl.dict.config.DynamicTableNameHandler$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 14:57:27.031 [36mINFO [m [35m[main][m [36mo.s.b.w.e.t.TomcatWebServer[m [34m[][m - [32mTomcat initialized with port(s): 28183 (http)
[m2025-09-18 14:57:27.086 [36mINFO [m [35m[main][m [36mo.a.c.h.Http11NioProtocol[m [34m[][m - [32mInitializing ProtocolHandler ["http-nio-28183"]
[m2025-09-18 14:57:27.096 [36mINFO [m [35m[main][m [36mo.a.c.c.StandardService[m [34m[][m - [32mStarting service [Tomcat]
[m2025-09-18 14:57:27.096 [36mINFO [m [35m[main][m [36mo.a.c.c.StandardEngine[m [34m[][m - [32mStarting Servlet engine: [Apache Tomcat/9.0.83]
[m2025-09-18 14:57:27.524 [36mINFO [m [35m[main][m [36mo.a.c.c.C.[.[.[/][m [34m[][m - [32mInitializing Spring embedded WebApplicationContext
[m2025-09-18 14:57:27.525 [36mINFO [m [35m[main][m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[m [34m[][m - [32mRoot WebApplicationContext: initialization completed in 8534 ms
[m2025-09-18 14:57:28.063 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'sso-hl' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 14:57:29.121 [36mINFO [m [35m[main][m [36mc.h.d.c.MybatisPlusEnhancerConfiguration[m [34m[][m - [32m✅ Enhanced MybatisPlusInterceptor with DynamicTableNameInnerInterceptor.
[m2025-09-18 14:57:33.289 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-18 14:57:33.290 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.307 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-18 14:57:33.308 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.325 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-18 14:57:33.325 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.344 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-18 14:57:33.345 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.360 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-18 14:57:33.362 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.380 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-18 14:57:33.381 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.397 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-18 14:57:33.398 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.413 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-18 14:57:33.414 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.431 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-18 14:57:33.431 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.448 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-18 14:57:33.449 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.467 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-18 14:57:33.467 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.498 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-18 14:57:33.499 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.518 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-18 14:57:33.552 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-18 14:57:33.553 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.575 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-18 14:57:33.575 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.592 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-18 14:57:33.593 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.612 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjLzfx".
[m2025-09-18 14:57:33.613 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjLzfx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.631 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-18 14:57:33.631 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.651 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-18 14:57:33.651 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.672 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-18 14:57:33.673 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.693 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-18 14:57:33.694 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.713 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-18 14:57:33.714 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.729 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-18 14:57:33.730 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.745 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-18 14:57:33.745 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.761 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-18 14:57:33.761 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.798 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-18 14:57:33.798 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.823 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-18 14:57:33.823 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.840 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-18 14:57:33.840 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.855 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-18 14:57:33.855 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.872 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-18 14:57:33.872 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.887 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-18 14:57:33.888 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.904 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-18 14:57:33.905 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.920 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-18 14:57:33.921 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.936 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-18 14:57:33.936 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.951 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-18 14:57:33.952 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.969 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-18 14:57:33.969 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:33.985 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-18 14:57:33.986 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.002 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-18 14:57:34.003 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.017 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-18 14:57:34.017 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.044 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-18 14:57:34.045 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.065 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-18 14:57:34.065 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.084 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-18 14:57:34.085 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.102 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-18 14:57:34.103 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.122 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-18 14:57:34.122 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.140 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-18 14:57:34.142 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.157 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-18 14:57:34.158 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.175 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-18 14:57:34.175 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.191 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-18 14:57:34.192 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.212 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjYjbb".
[m2025-09-18 14:57:34.212 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjYjbb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.229 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-18 14:57:34.229 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.270 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-18 14:57:34.271 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.301 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-18 14:57:34.301 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.330 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-18 14:57:34.331 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.349 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-18 14:57:34.350 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:34.367 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-18 14:57:34.367 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 14:57:35.461 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'hl-task' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 14:57:52.007 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:57:55.549 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:57:55.549 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:57:55.663 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:57:58.709 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:57:58.711 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:57:58.839 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:58:01.865 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:58:01.865 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:58:01.995 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:58:05.024 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:58:05.025 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:58:05.161 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:58:08.199 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:58:08.200 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:58:08.353 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:58:11.394 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:58:11.395 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:58:11.527 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:58:14.560 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:58:14.560 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:58:14.689 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:58:17.718 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:58:17.720 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:58:18.186 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CG9Z9HJ3FJW -> BusinessTripStrategy
[m2025-09-18 14:58:18.186 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: C09ZZEL29S8 -> LeaveChangZhouStrategy
[m2025-09-18 14:58:18.186 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CC42ITDEHRQ -> LeaveRequestStrategy
[m2025-09-18 14:58:18.187 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CKSWWQQWW7H -> OverseasTravelStrategy
[m2025-09-18 14:58:18.187 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CNE2TZD2GTE -> PoliceAbilityStrategy
[m2025-09-18 14:58:18.187 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CXQNJMIAR1C -> PoliceClubActivityStrategy
[m2025-09-18 14:58:18.187 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: C30NIBEJEGI -> PoliceDrinkReportStrategy
[m2025-09-18 14:58:18.397 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:58:21.421 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:58:21.421 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:58:21.728 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 14:58:24.761 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 14:58:24.761 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 14:58:41.527 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 14:58:41.847 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 14:58:41.848 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 14:58:42.814 [36mINFO [m [35m[main][m [36mc.h.n.c.SharedNacosNameSpace[m [34m[][m - [32m{"secretKey":"","password":"hl123","namespace":"public","accessKey":"","serverAddr":"**************:8848","isUseCloudNamespaceParsing":"false","clusterName":"","username":"nacos"}
[m2025-09-18 14:58:44.034 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 14:58:44.813 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'hl-dict' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 14:58:49.877 [36mINFO [m [35m[main][m [36mo.s.b.a.e.w.EndpointLinksResolver[m [34m[][m - [32mExposing 1 endpoint(s) beneath base path '/actuator'
[m2025-09-18 14:58:51.472 [36mINFO [m [35m[main][m [36mc.h.s.c.SecurityConfig[m [34m[][m - [32m[/error/logs, /test/dict, /dict/api-query, /dict/add-batch, /dict-test/test] --> 200
[m2025-09-18 14:58:51.844 [36mINFO [m [35m[main][m [36mo.s.s.w.DefaultSecurityFilterChain[m [34m[][m - [32mWill secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@60dc3a85, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@20c4564e, org.springframework.security.web.context.SecurityContextPersistenceFilter@560b74b8, org.springframework.security.web.header.HeaderWriterFilter@37878749, org.springframework.security.web.authentication.logout.LogoutFilter@3ece2e4, org.springframework.web.filter.CorsFilter@122c4c32, com.hl.security.config.sso.SsoAuthTokenFilter@105e9fef, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@668d6fe9, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@bd15940, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4dcd5930, org.springframework.security.web.session.SessionManagementFilter@5fa47c24, org.springframework.security.web.access.ExceptionTranslationFilter@3efcacdc, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2c5b1fe5]
[m2025-09-18 14:58:59.650 [36mINFO [m [35m[main][m [36mo.a.c.h.Http11NioProtocol[m [34m[][m - [32mStarting ProtocolHandler ["http-nio-28183"]
[m2025-09-18 14:58:59.805 [36mINFO [m [35m[main][m [36mo.s.b.w.e.t.TomcatWebServer[m [34m[][m - [32mTomcat started on port(s): 28183 (http) with context path ''
[m2025-09-18 14:59:01.017 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 14:59:01.026 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 14:59:01.027 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 14:59:01.339 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mnacos registry, default hl-wj-police-archive *************:28183 register finished
[m2025-09-18 14:59:05.882 [36mINFO [m [35m[main][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 14:59:06.287 [36mINFO [m [35m[main][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mCreated new connection: rabbitConnectionFactory#453b1c5:0/SimpleConnection@4c43e0f4 [delegate=amqp://admin@**************:5672/, localPort= 56830]
[m2025-09-18 14:59:06.905 [36mINFO [m [35m[main][m [36mc.h.AppMain[m [34m[][m - [32mStarted AppMain in 116.462 seconds (JVM running for 121.827)
[m2025-09-18 14:59:06.908 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 14:59:06.908 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 14:59:11.098 [36mINFO [m [35m[main][m [36mc.h.s.c.s.c.SsoCache[m [34m[][m - [32minit cache_data -> size 21 --> ["user_circle","role","org_job_user","user_resources","project","resources","user_leave","resources_user","user_all","user_role_meet","circle_user","user_role","role_resources","police","role_user","organization","user_org_role","job","circle","user","organization_tree"] -> 
[m2025-09-18 14:59:11.104 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 14:59:11.104 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 14:59:11.398 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive.properties, group=default
[m2025-09-18 14:59:11.398 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive-druid.properties, group=default
[m2025-09-18 14:59:11.398 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive, group=default
[m2025-09-18 14:59:11.469 [36mINFO [m [35m[main][m [36mc.h.d.s.i.DictDataServiceImpl[m [34m[][m - [32mrefresh dict cache 
[m2025-09-18 14:59:12.036 [36mINFO [m [35m[RMI TCP Connection(3)-*************][m [36mo.a.c.c.C.[.[.[/][m [34m[][m - [32mInitializing Spring DispatcherServlet 'dispatcherServlet'
[m2025-09-18 14:59:12.042 [36mINFO [m [35m[RMI TCP Connection(3)-*************][m [36mo.s.w.s.DispatcherServlet[m [34m[][m - [32mInitializing Servlet 'dispatcherServlet'
[m2025-09-18 14:59:12.090 [36mINFO [m [35m[RMI TCP Connection(3)-*************][m [36mo.s.w.s.DispatcherServlet[m [34m[][m - [32mCompleted initialization in 47 ms
[m2025-09-18 14:59:12.946 [36mINFO [m [35m[main][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：SELECT id,dict_type,parent_id,dict_value,dict_name,sort,field_class,remark,create_user,create_time,update_user,update_time,is_disable,tenant_id,is_system,is_default,extend FROM dict_data
[m2025-09-18 14:59:13.186 [33mWARN [m [35m[RMI TCP Connection(5)-*************][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor143.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-18 14:59:18.914 [31mERROR[m [35m[main][m [36mc.a.d.f.s.StatFilter[m [34m[][m - [31mslow sql 1879 millis. SELECT 1[]
[m2025-09-18 14:59:22.483 [36mINFO [m [35m[main][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} inited
[m2025-09-18 14:59:26.962 [36mINFO [m [35m[RMI TCP Connection(5)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} inited
[m2025-09-18 14:59:31.281 [36mINFO [m [35m[RMI TCP Connection(5)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} inited
[m2025-09-18 14:59:36.691 [36mINFO [m [35m[RMI TCP Connection(5)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} inited
[m2025-09-18 14:59:55.922 [36mINFO [m [35m[http-nio-28183-exec-1][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：SELECT ability_tag_code, ability_tag_name, COUNT(*) as count FROM police_ability_tag WHERE is_deleted = 0 AND ability_tag_code IS NOT NULL AND ability_tag_code != '' GROUP BY ability_tag_code, ability_tag_name ORDER BY COUNT(*) DESC
[m2025-09-18 14:59:56.201 [33mWARN [m [35m[http-nio-28183-exec-1][m [36mc.h.a.s.PoliceAbilityTagService[m [34m[][m - [33m无法获取字典代码 1943153463189860353 的层级信息
[m2025-09-18 14:59:56.202 [33mWARN [m [35m[http-nio-28183-exec-1][m [36mc.h.a.s.PoliceAbilityTagService[m [34m[][m - [33m无法获取字典代码 1943152464437997570 的层级信息
[m2025-09-18 14:59:56.479 [36mINFO [m [35m[http-nio-28183-exec-1][m [36mc.h.l.LogSlf4j[m [34m[][m - [32m127.0.0.1:/abilityTag/statistics (局长指令) -> {} -> 366ms -> {"count":0,"data":[],"errno":200,"error":"操作成功"}
[m2025-09-18 15:00:02.519 [36mINFO [m [35m[scheduling-1][m [36mc.h.s.c.s.c.SsoCache[m [34m[][m - [32minit cache_data -> size 21 --> ["user_circle","role","org_job_user","user_resources","project","resources","user_leave","resources_user","user_all","user_role_meet","circle_user","user_role","role_resources","police","role_user","organization","user_org_role","job","circle","user","organization_tree"] -> 
[m2025-09-18 15:00:10.325 [36mINFO [m [35m[http-nio-28183-exec-19][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：SELECT ability_tag_code, ability_tag_name, COUNT(*) as count FROM police_ability_tag WHERE is_deleted = 0 AND ability_tag_code IS NOT NULL AND ability_tag_code != '' GROUP BY ability_tag_code, ability_tag_name ORDER BY COUNT(*) DESC
[m2025-09-18 15:00:10.331 [33mWARN [m [35m[http-nio-28183-exec-19][m [36mc.h.a.s.PoliceAbilityTagService[m [34m[][m - [33m无法获取字典代码 1943153463189860353 的层级信息
[m2025-09-18 15:00:10.331 [33mWARN [m [35m[http-nio-28183-exec-19][m [36mc.h.a.s.PoliceAbilityTagService[m [34m[][m - [33m无法获取字典代码 1943152464437997570 的层级信息
[m2025-09-18 15:00:10.342 [36mINFO [m [35m[http-nio-28183-exec-19][m [36mc.h.l.LogSlf4j[m [34m[][m - [32m127.0.0.1:/abilityTag/statistics (局长指令) -> {} -> 6ms -> {"count":0,"data":[],"errno":200,"error":"操作成功"}
[m2025-09-18 15:00:35.425 [36mINFO [m [35m[http-nio-28183-exec-12][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：SELECT ability_tag_code, ability_tag_name, COUNT(*) as count FROM police_ability_tag WHERE is_deleted = 0 AND ability_tag_code IS NOT NULL AND ability_tag_code != '' GROUP BY ability_tag_code, ability_tag_name ORDER BY COUNT(*) DESC
[m2025-09-18 15:01:21.487 [33mWARN [m [35m[http-nio-28183-exec-12][m [36mc.h.a.s.PoliceAbilityTagService[m [34m[][m - [33m无法获取字典代码 1943153463189860353 的层级信息
[m2025-09-18 15:01:24.793 [33mWARN [m [35m[http-nio-28183-exec-12][m [36mc.h.a.s.PoliceAbilityTagService[m [34m[][m - [33m无法获取字典代码 1943152464437997570 的层级信息
[m2025-09-18 15:01:24.794 [36mINFO [m [35m[http-nio-28183-exec-12][m [36mc.h.l.LogSlf4j[m [34m[][m - [32m127.0.0.1:/abilityTag/statistics (局长指令) -> {} -> 49370ms -> {"count":0,"data":[],"errno":200,"error":"操作成功"}
[m2025-09-18 15:01:36.402 [36mINFO [m [35m[http-nio-28183-exec-3][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Redis repositories in DEFAULT mode.
[m2025-09-18 15:01:36.410 [36mINFO [m [35m[http-nio-28183-exec-3][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 7 ms. Found 0 Redis repository interfaces.
[m2025-09-18 15:01:36.411 [36mINFO [m [35m[http-nio-28183-exec-3][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 15:01:36.417 [36mINFO [m [35m[http-nio-28183-exec-3][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 4 ms. Found 0 Reactive Elasticsearch repository interfaces.
[m2025-09-18 15:01:36.439 [36mINFO [m [35m[http-nio-28183-exec-3][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 15:01:36.445 [36mINFO [m [35m[http-nio-28183-exec-3][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 4 ms. Found 0 Elasticsearch repository interfaces.
[m2025-09-18 15:01:36.448 [33mWARN [m [35m[http-nio-28183-exec-3][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mNo MyBatis mapper was found in '[com.hl.**.mapper]' package. Please check your configuration.
[m2025-09-18 15:01:36.569 [36mINFO [m [35m[http-nio-28183-exec-3][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：SELECT ability_tag_code, ability_tag_name, COUNT(*) as count FROM police_ability_tag WHERE is_deleted = 0 AND ability_tag_code IS NOT NULL AND ability_tag_code != '' GROUP BY ability_tag_code, ability_tag_name ORDER BY COUNT(*) DESC
[m2025-09-18 15:01:36.701 [36mINFO [m [35m[http-nio-28183-exec-3][m [36mc.h.l.LogSlf4j[m [34m[][m - [32m127.0.0.1:/abilityTag/statistics (局长指令) -> {} -> 87ms -> {"count":0,"data":[{"categoryName":"部省两级公安机关","children":[{"categoryName":"公安机关专业技术能力评定事项","children":[{"categoryName":"公安部（或与人社、教育等部门一同）组织的专业技术能力评定事项","children":[{"categoryName":"执法资格（高级）","children":[],"count":173,"dictCode":"1943153463189860353","isLeaf":true,"level":3,"levelPath":["部省两级公安机关","公安机关专业技术能力评定事项","公安部（或与人社、教育等部门一同）组织的专业技术能力评定事项","执法资格（高级）"]}],"count":173,"isLeaf":false,"level":2,"levelPath":["部省两级公安机关","公安机关专业技术能力评定事项","公安部（或与人社、教育等部门一同）组织的专业技术能力评定事项"]}],"count":173,"isLeaf":false,"level":1,"levelPath":["部省两级公安机关","公安机关专业技术能力评定事项"]}],"count":173,"isLeaf":false,"level":0,"levelPath":["部省两级公安机关"]},{"categoryName":"人社部门","children":[{"categoryName":"职业资格证书","children":[{"categoryName":"国家职业资格","children":[{"categoryName":"法律职业资格","children":[],"count":1,"dictCode":"1943152464437997570","isLeaf":true,"level":3,"levelPath":["人社部门","职业资格证书","国家职业资格","法律职业资格"]}],"count":1,"isLeaf":false,"level":2,"levelPath":["人社部门","职业资格证书","国家职业资格"]}],"count":1,"isLeaf":false,"level":1,"lev
[m2025-09-18 15:36:22.907 [33mWARN [m [35m[Thread-25][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-18 15:36:22.906 [33mWARN [m [35m[Thread-32][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-18 15:36:22.907 [33mWARN [m [35m[Thread-32][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-18 15:36:22.909 [33mWARN [m [35m[Thread-25][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-18 15:36:22.971 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mWaiting for workers to finish.
[m2025-09-18 15:36:22.973 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mo.s.b.w.e.t.GracefulShutdown[m [34m[][m - [32mCommencing graceful shutdown. Waiting for active requests to complete
[m2025-09-18 15:36:23.100 [36mINFO [m [35m[tomcat-shutdown][m [36mo.s.b.w.e.t.GracefulShutdown[m [34m[][m - [32mGraceful shutdown complete
[m2025-09-18 15:36:23.577 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mSuccessfully waited for workers to finish.
[m2025-09-18 15:36:23.642 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mDe-registering from Nacos Server now...
[m2025-09-18 15:36:23.649 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mDe-registration finished.
[m2025-09-18 15:36:24.006 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.h.c.t.GraceThreadPool[m [34m[][m - [32m==== 优雅关闭线程池 ====
[m2025-09-18 15:36:24.032 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} closing ...
[m2025-09-18 15:36:24.043 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} closed
[m2025-09-18 15:36:24.043 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} closing ...
[m2025-09-18 15:36:24.044 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} closed
[m2025-09-18 15:36:24.044 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} closing ...
[m2025-09-18 15:36:24.046 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} closed
[m2025-09-18 15:36:24.047 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} closing ...
[m2025-09-18 15:36:24.048 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} closed
[m2025-09-18 15:36:36.193 [36mINFO [m [35m[main][m [36mc.a.n.c.e.SearchableProperties[m [34m[][m - [32mproperties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[m2025-09-18 15:36:36.246 [36mINFO [m [35m[background-preinit][m [36mo.h.v.i.u.Version[m [34m[][m - [32mHV000001: Hibernate Validator 6.2.5.Final
[m2025-09-18 15:36:39.018 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 15:36:39.018 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 15:36:42.664 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-18 15:36:42.673 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-18 15:36:42.678 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-18 15:36:42.680 [36mINFO [m [35m[main][m [36mo.s.c.b.c.PropertySourceBootstrapConfiguration[m [34m[][m - [32mLocated property source: [BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive-druid.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive,default'}]
[m2025-09-18 15:36:42.727 [36mINFO [m [35m[main][m [36mc.h.AppMain[m [34m[][m - [32mThe following 1 profile is active: "druid"
[m2025-09-18 15:36:45.703 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 15:36:45.715 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 15:36:45.817 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 75 ms. Found 0 Elasticsearch repository interfaces.
[m2025-09-18 15:36:45.826 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 15:36:45.827 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 15:36:45.867 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 39 ms. Found 0 Reactive Elasticsearch repository interfaces.
[m2025-09-18 15:36:45.887 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 15:36:45.892 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Redis repositories in DEFAULT mode.
[m2025-09-18 15:36:45.943 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 40 ms. Found 0 Redis repository interfaces.
[m2025-09-18 15:36:46.567 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:36:46.567 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:36:46.568 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:36:46.568 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:36:46.568 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:36:46.568 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:36:46.568 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:36:46.569 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:36:46.569 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:36:46.569 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:36:47.577 [36mINFO [m [35m[main][m [36mo.s.c.c.s.GenericScope[m [34m[][m - [32mBeanFactory id=6a689e8b-b3c8-374c-807c-3d8ec15b808c
[m2025-09-18 15:36:49.149 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:36:49.161 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:36:49.165 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$597/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:36:49.182 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:36:49.471 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'dictProperties' of type [com.hl.dict.config.DictProperties$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:36:49.478 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'dynamicTableNameHandler' of type [com.hl.dict.config.DynamicTableNameHandler$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:36:50.757 [36mINFO [m [35m[main][m [36mo.s.b.w.e.t.TomcatWebServer[m [34m[][m - [32mTomcat initialized with port(s): 28183 (http)
[m2025-09-18 15:36:50.807 [36mINFO [m [35m[main][m [36mo.a.c.h.Http11NioProtocol[m [34m[][m - [32mInitializing ProtocolHandler ["http-nio-28183"]
[m2025-09-18 15:36:50.812 [36mINFO [m [35m[main][m [36mo.a.c.c.StandardService[m [34m[][m - [32mStarting service [Tomcat]
[m2025-09-18 15:36:50.813 [36mINFO [m [35m[main][m [36mo.a.c.c.StandardEngine[m [34m[][m - [32mStarting Servlet engine: [Apache Tomcat/9.0.83]
[m2025-09-18 15:36:51.257 [36mINFO [m [35m[main][m [36mo.a.c.c.C.[.[.[/][m [34m[][m - [32mInitializing Spring embedded WebApplicationContext
[m2025-09-18 15:36:51.257 [36mINFO [m [35m[main][m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[m [34m[][m - [32mRoot WebApplicationContext: initialization completed in 8502 ms
[m2025-09-18 15:36:51.796 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'sso-hl' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 15:36:52.849 [36mINFO [m [35m[main][m [36mc.h.d.c.MybatisPlusEnhancerConfiguration[m [34m[][m - [32m✅ Enhanced MybatisPlusInterceptor with DynamicTableNameInnerInterceptor.
[m2025-09-18 15:36:56.995 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-18 15:36:56.996 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.013 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-18 15:36:57.013 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.031 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-18 15:36:57.031 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.049 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-18 15:36:57.049 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.065 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-18 15:36:57.065 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.083 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-18 15:36:57.083 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.100 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-18 15:36:57.100 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.116 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-18 15:36:57.117 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.136 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-18 15:36:57.136 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.153 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-18 15:36:57.153 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.172 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-18 15:36:57.172 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.190 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-18 15:36:57.191 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.210 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-18 15:36:57.247 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-18 15:36:57.248 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.275 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-18 15:36:57.275 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.291 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-18 15:36:57.291 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.324 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjLzfx".
[m2025-09-18 15:36:57.325 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjLzfx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.342 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-18 15:36:57.344 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.364 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-18 15:36:57.364 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.383 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-18 15:36:57.384 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.405 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-18 15:36:57.405 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.424 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-18 15:36:57.424 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.440 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-18 15:36:57.442 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.456 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-18 15:36:57.456 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.474 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-18 15:36:57.474 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.511 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-18 15:36:57.511 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.537 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-18 15:36:57.537 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.552 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-18 15:36:57.552 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.567 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-18 15:36:57.568 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.584 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-18 15:36:57.585 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.600 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-18 15:36:57.600 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.617 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-18 15:36:57.618 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.635 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-18 15:36:57.636 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.652 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-18 15:36:57.652 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.669 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-18 15:36:57.669 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.683 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-18 15:36:57.685 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.701 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-18 15:36:57.701 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.716 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-18 15:36:57.717 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.731 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-18 15:36:57.732 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.758 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-18 15:36:57.759 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.781 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-18 15:36:57.781 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.798 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-18 15:36:57.798 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.816 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-18 15:36:57.816 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.836 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-18 15:36:57.836 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.856 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-18 15:36:57.856 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.873 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-18 15:36:57.874 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.889 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-18 15:36:57.890 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.907 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-18 15:36:57.907 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.927 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjYjbb".
[m2025-09-18 15:36:57.927 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjYjbb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.945 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-18 15:36:57.945 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:57.973 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-18 15:36:57.974 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:58.004 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-18 15:36:58.005 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:58.035 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-18 15:36:58.035 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:58.053 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-18 15:36:58.053 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:58.071 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-18 15:36:58.071 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:36:59.179 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'hl-task' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 15:37:16.317 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:37:19.890 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:37:19.890 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:37:20.005 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:37:24.049 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:37:24.049 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:37:24.180 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:37:27.217 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:37:27.217 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:37:27.352 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:37:30.384 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:37:30.385 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:37:30.522 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:37:33.563 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:37:33.564 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:37:33.699 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:37:36.735 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:37:36.735 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:37:36.869 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:37:39.903 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:37:39.903 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:37:40.031 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:37:43.087 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:37:43.089 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:37:43.555 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CG9Z9HJ3FJW -> BusinessTripStrategy
[m2025-09-18 15:37:43.556 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: C09ZZEL29S8 -> LeaveChangZhouStrategy
[m2025-09-18 15:37:43.556 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CC42ITDEHRQ -> LeaveRequestStrategy
[m2025-09-18 15:37:43.556 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CKSWWQQWW7H -> OverseasTravelStrategy
[m2025-09-18 15:37:43.556 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CNE2TZD2GTE -> PoliceAbilityStrategy
[m2025-09-18 15:37:43.556 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CXQNJMIAR1C -> PoliceClubActivityStrategy
[m2025-09-18 15:37:43.557 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: C30NIBEJEGI -> PoliceDrinkReportStrategy
[m2025-09-18 15:37:43.775 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:37:46.807 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:37:46.808 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:37:47.122 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:37:50.148 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:37:50.149 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-2][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:38:06.885 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 15:38:07.211 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 15:38:07.211 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 15:38:08.185 [36mINFO [m [35m[main][m [36mc.h.n.c.SharedNacosNameSpace[m [34m[][m - [32m{"secretKey":"","password":"hl123","namespace":"public","accessKey":"","serverAddr":"**************:8848","isUseCloudNamespaceParsing":"false","clusterName":"","username":"nacos"}
[m2025-09-18 15:38:09.441 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 15:38:10.222 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'hl-dict' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 15:38:15.253 [36mINFO [m [35m[main][m [36mo.s.b.a.e.w.EndpointLinksResolver[m [34m[][m - [32mExposing 1 endpoint(s) beneath base path '/actuator'
[m2025-09-18 15:38:16.821 [36mINFO [m [35m[main][m [36mc.h.s.c.SecurityConfig[m [34m[][m - [32m[/error/logs, /dict/add-batch, /dict-test/test, /dict/api-query, /test/dict] --> 200
[m2025-09-18 15:38:17.238 [36mINFO [m [35m[main][m [36mo.s.s.w.DefaultSecurityFilterChain[m [34m[][m - [32mWill secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@7288d563, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@56b1f2ea, org.springframework.security.web.context.SecurityContextPersistenceFilter@21e52195, org.springframework.security.web.header.HeaderWriterFilter@6430070f, org.springframework.security.web.authentication.logout.LogoutFilter@6a4cdeb0, org.springframework.web.filter.CorsFilter@4b60cdd0, com.hl.security.config.sso.SsoAuthTokenFilter@106e816c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@21459683, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@31cffb27, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@38a1d9ff, org.springframework.security.web.session.SessionManagementFilter@8262864, org.springframework.security.web.access.ExceptionTranslationFilter@6776c0ee, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@2a18e736]
[m2025-09-18 15:38:24.749 [36mINFO [m [35m[main][m [36mo.a.c.h.Http11NioProtocol[m [34m[][m - [32mStarting ProtocolHandler ["http-nio-28183"]
[m2025-09-18 15:38:24.900 [36mINFO [m [35m[main][m [36mo.s.b.w.e.t.TomcatWebServer[m [34m[][m - [32mTomcat started on port(s): 28183 (http) with context path ''
[m2025-09-18 15:38:26.111 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 15:38:26.129 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 15:38:26.129 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 15:38:26.400 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mnacos registry, default hl-wj-police-archive *************:28183 register finished
[m2025-09-18 15:38:30.900 [36mINFO [m [35m[main][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 15:38:31.300 [36mINFO [m [35m[main][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mCreated new connection: rabbitConnectionFactory#ac85403:0/SimpleConnection@44454f80 [delegate=amqp://admin@**************:5672/, localPort= 61114]
[m2025-09-18 15:38:31.923 [36mINFO [m [35m[main][m [36mc.h.AppMain[m [34m[][m - [32mStarted AppMain in 117.727 seconds (JVM running for 123.146)
[m2025-09-18 15:38:31.925 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 15:38:31.925 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 15:38:36.062 [36mINFO [m [35m[main][m [36mc.h.s.c.s.c.SsoCache[m [34m[][m - [32minit cache_data -> size 21 --> ["user_circle","role","org_job_user","user_resources","project","resources","user_leave","resources_user","user_all","user_role_meet","circle_user","user_role","role_resources","police","role_user","organization","user_org_role","job","circle","user","organization_tree"] -> 
[m2025-09-18 15:38:36.069 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 15:38:36.069 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 15:38:36.364 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive.properties, group=default
[m2025-09-18 15:38:36.366 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive-druid.properties, group=default
[m2025-09-18 15:38:36.366 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive, group=default
[m2025-09-18 15:38:36.436 [36mINFO [m [35m[main][m [36mc.h.d.s.i.DictDataServiceImpl[m [34m[][m - [32mrefresh dict cache 
[m2025-09-18 15:38:37.379 [36mINFO [m [35m[RMI TCP Connection(4)-*************][m [36mo.a.c.c.C.[.[.[/][m [34m[][m - [32mInitializing Spring DispatcherServlet 'dispatcherServlet'
[m2025-09-18 15:38:37.379 [36mINFO [m [35m[RMI TCP Connection(4)-*************][m [36mo.s.w.s.DispatcherServlet[m [34m[][m - [32mInitializing Servlet 'dispatcherServlet'
[m2025-09-18 15:38:37.461 [36mINFO [m [35m[RMI TCP Connection(4)-*************][m [36mo.s.w.s.DispatcherServlet[m [34m[][m - [32mCompleted initialization in 82 ms
[m2025-09-18 15:38:37.621 [36mINFO [m [35m[main][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：SELECT id,dict_type,parent_id,dict_value,dict_name,sort,field_class,remark,create_user,create_time,update_user,update_time,is_disable,tenant_id,is_system,is_default,extend FROM dict_data
[m2025-09-18 15:38:38.387 [33mWARN [m [35m[RMI TCP Connection(2)-*************][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor144.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-18 15:38:44.632 [31mERROR[m [35m[main][m [36mc.a.d.f.s.StatFilter[m [34m[][m - [31mslow sql 1848 millis. SELECT 1[]
[m2025-09-18 15:38:48.179 [36mINFO [m [35m[main][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} inited
[m2025-09-18 15:38:52.625 [36mINFO [m [35m[RMI TCP Connection(2)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} inited
[m2025-09-18 15:38:56.989 [36mINFO [m [35m[RMI TCP Connection(2)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} inited
[m2025-09-18 15:39:02.430 [36mINFO [m [35m[RMI TCP Connection(2)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} inited
[m2025-09-18 15:39:38.479 [36mINFO [m [35m[http-nio-28183-exec-1][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：select category_name, feature_name from police_capability_eval group by category_name, feature_name
[m2025-09-18 15:39:38.770 [36mINFO [m [35m[http-nio-28183-exec-1][m [36mc.h.l.LogSlf4j[m [34m[][m - [32m127.0.0.1:/capabilityEval/tree (局长指令) ->  -> 195ms -> {"count":0,"data":[{"id":"1","name":"专业打击","parentId":"0","weight":0},{"id":"2","name":"侦查研判","parentId":"1","weight":2},{"id":"2","name":"检验鉴定","parentId":"1","weight":2},{"id":"2","name":"监所安全管理","parentId":"1","weight":2},{"id":"2","name":"获取线索","parentId":"1","weight":2},{"id":"2","name":"审讯突破","parentId":"1","weight":2},{"id":"2","name":"实战指挥","parentId":"1","weight":2},{"id":"2","name":"现场勘查","parentId":"1","weight":2},{"id":"2","name":"监所协助破案","parentId":"1","weight":2},{"id":"2","name":"执法监督","parentId":"0","weight":0},{"id":"3","name":"案件审核（办理）","parentId":"1","weight":3},{"id":"3","name":"数字督察","parentId":"1","weight":3},{"id":"3","name":"执法监督指导","parentId":"1","weight":3},{"id":"3","name":"现场督察","parentId":"1","weight":3},{"id":"3","name":"信访举报核查","parentId":"1","weight":3},{"id":"3","name":"执纪办案","parentId":"1","weight":3},{"id":"3","name":"情指作战","parentId":"0","weight":0},{"id":"4","name":"巡防综合能力","parentId":"1","weight":4},{"id":"4","name":"110接处警","parentId":"1","weight"
[m2025-09-18 15:40:45.218 [36mINFO [m [35m[http-nio-28183-exec-13][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 15:40:45.229 [36mINFO [m [35m[http-nio-28183-exec-13][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 10 ms. Found 0 Elasticsearch repository interfaces.
[m2025-09-18 15:40:45.235 [33mWARN [m [35m[http-nio-28183-exec-13][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mNo MyBatis mapper was found in '[com.hl.**.mapper]' package. Please check your configuration.
[m2025-09-18 15:40:45.253 [36mINFO [m [35m[http-nio-28183-exec-13][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Redis repositories in DEFAULT mode.
[m2025-09-18 15:40:45.258 [36mINFO [m [35m[http-nio-28183-exec-13][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 4 ms. Found 0 Redis repository interfaces.
[m2025-09-18 15:40:45.285 [36mINFO [m [35m[http-nio-28183-exec-13][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 15:40:45.289 [36mINFO [m [35m[http-nio-28183-exec-13][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 2 ms. Found 0 Reactive Elasticsearch repository interfaces.
[m2025-09-18 15:40:45.998 [36mINFO [m [35m[http-nio-28183-exec-13][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：select category_name, feature_name from police_capability_eval group by category_name, feature_name
[m2025-09-18 15:40:46.686 [36mINFO [m [35m[http-nio-28183-exec-13][m [36mc.h.l.LogSlf4j[m [34m[][m - [32m127.0.0.1:/capabilityEval/tree (局长指令) ->  -> 831ms -> {"count":0,"data":[{"id":"1","parentId":"0","weight":0,"name":"专业打击","children":[{"id":"8","parentId":"1","weight":8,"name":"数据建模与实战应用"}]},{"id":"2","parentId":"0","weight":0,"name":"执法监督"},{"id":"3","parentId":"0","weight":0,"name":"情指作战"},{"id":"4","parentId":"0","weight":0,"name":"综合保障"},{"id":"5","parentId":"0","weight":0,"name":"基础防控"},{"id":"6","parentId":"0","weight":0,"name":"交通治理"},{"id":"7","parentId":"0","weight":0,"name":"智慧警务"}],"errno":200,"error":"操作成功"}
[m2025-09-18 15:41:48.172 [36mINFO [m [35m[http-nio-28183-exec-3][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：select category_name, feature_name from police_capability_eval group by category_name, feature_name
[m2025-09-18 15:43:02.477 [36mINFO [m [35m[http-nio-28183-exec-3][m [36mc.h.l.LogSlf4j[m [34m[][m - [32m127.0.0.1:/capabilityEval/tree (局长指令) ->  -> 74303ms -> {"count":0,"data":[{"id":"1","parentId":"0","weight":0,"name":"专业打击","children":[{"id":"8","parentId":"1","weight":8,"name":"数据建模与实战应用"}]},{"id":"2","parentId":"0","weight":0,"name":"执法监督"},{"id":"3","parentId":"0","weight":0,"name":"情指作战"},{"id":"4","parentId":"0","weight":0,"name":"综合保障"},{"id":"5","parentId":"0","weight":0,"name":"基础防控"},{"id":"6","parentId":"0","weight":0,"name":"交通治理"},{"id":"7","parentId":"0","weight":0,"name":"智慧警务"}],"errno":200,"error":"操作成功"}
[m2025-09-18 15:43:09.575 [36mINFO [m [35m[http-nio-28183-exec-14][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 15:43:09.579 [36mINFO [m [35m[http-nio-28183-exec-14][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 4 ms. Found 0 Elasticsearch repository interfaces.
[m2025-09-18 15:43:09.583 [33mWARN [m [35m[http-nio-28183-exec-14][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mNo MyBatis mapper was found in '[com.hl.**.mapper]' package. Please check your configuration.
[m2025-09-18 15:43:09.597 [36mINFO [m [35m[http-nio-28183-exec-14][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Redis repositories in DEFAULT mode.
[m2025-09-18 15:43:09.601 [36mINFO [m [35m[http-nio-28183-exec-14][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
[m2025-09-18 15:43:09.977 [36mINFO [m [35m[http-nio-28183-exec-14][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 15:43:09.982 [36mINFO [m [35m[http-nio-28183-exec-14][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 4 ms. Found 0 Reactive Elasticsearch repository interfaces.
[m2025-09-18 15:43:10.250 [36mINFO [m [35m[http-nio-28183-exec-14][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：select category_name, feature_name from police_capability_eval group by category_name, feature_name
[m2025-09-18 15:43:10.266 [36mINFO [m [35m[http-nio-28183-exec-14][m [36mc.h.l.LogSlf4j[m [34m[][m - [32m127.0.0.1:/capabilityEval/tree (局长指令) ->  -> 80ms -> {"count":0,"data":[{"id":"1","parentId":"0","weight":0,"name":"专业打击"},{"id":"2","parentId":"0","weight":0,"name":"执法监督"},{"id":"3","parentId":"0","weight":0,"name":"情指作战"},{"id":"4","parentId":"0","weight":0,"name":"综合保障"},{"id":"5","parentId":"0","weight":0,"name":"基础防控"},{"id":"6","parentId":"0","weight":0,"name":"交通治理"},{"id":"7","parentId":"0","weight":0,"name":"智慧警务","children":[{"id":"8","parentId":"7","weight":8,"name":"数据建模与实战应用"}]}],"errno":200,"error":"操作成功"}
[m2025-09-18 15:43:44.676 [36mINFO [m [35m[http-nio-28183-exec-17][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：select category_name, feature_name from police_capability_eval group by category_name, feature_name
[m2025-09-18 15:44:39.686 [36mINFO [m [35m[http-nio-28183-exec-17][m [36mc.h.l.LogSlf4j[m [34m[][m - [32m127.0.0.1:/capabilityEval/tree (局长指令) ->  -> 55011ms -> {"count":0,"data":[{"id":"1","parentId":"0","weight":0,"name":"专业打击"},{"id":"2","parentId":"0","weight":0,"name":"执法监督"},{"id":"3","parentId":"0","weight":0,"name":"情指作战"},{"id":"4","parentId":"0","weight":0,"name":"综合保障"},{"id":"5","parentId":"0","weight":0,"name":"基础防控"},{"id":"6","parentId":"0","weight":0,"name":"交通治理"},{"id":"7","parentId":"0","weight":0,"name":"智慧警务","children":[{"id":"8","parentId":"7","weight":8,"name":"数据建模与实战应用"}]}],"errno":200,"error":"操作成功"}
[m2025-09-18 15:44:57.754 [36mINFO [m [35m[http-nio-28183-exec-21][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：select category_name, feature_name from police_capability_eval group by category_name, feature_name
[m2025-09-18 15:45:01.767 [36mINFO [m [35m[http-nio-28183-exec-21][m [36mc.h.l.LogSlf4j[m [34m[][m - [32m127.0.0.1:/capabilityEval/tree (局长指令) ->  -> 4013ms -> {"count":0,"data":[{"id":"1","parentId":"0","weight":0,"name":"专业打击"},{"id":"2","parentId":"0","weight":0,"name":"执法监督"},{"id":"3","parentId":"0","weight":0,"name":"情指作战"},{"id":"4","parentId":"0","weight":0,"name":"综合保障"},{"id":"5","parentId":"0","weight":0,"name":"基础防控"},{"id":"6","parentId":"0","weight":0,"name":"交通治理"},{"id":"7","parentId":"0","weight":0,"name":"智慧警务","children":[{"id":"8","parentId":"7","weight":8,"name":"数据建模与实战应用"}]}],"errno":200,"error":"操作成功"}
[m2025-09-18 15:45:07.062 [33mWARN [m [35m[Thread-32][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-18 15:45:07.062 [33mWARN [m [35m[Thread-25][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-18 15:45:07.063 [33mWARN [m [35m[Thread-32][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-18 15:45:07.064 [33mWARN [m [35m[Thread-25][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-18 15:45:07.124 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mWaiting for workers to finish.
[m2025-09-18 15:45:07.126 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mo.s.b.w.e.t.GracefulShutdown[m [34m[][m - [32mCommencing graceful shutdown. Waiting for active requests to complete
[m2025-09-18 15:45:07.256 [36mINFO [m [35m[tomcat-shutdown][m [36mo.s.b.w.e.t.GracefulShutdown[m [34m[][m - [32mGraceful shutdown complete
[m2025-09-18 15:45:07.875 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mSuccessfully waited for workers to finish.
[m2025-09-18 15:45:07.936 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mDe-registering from Nacos Server now...
[m2025-09-18 15:45:07.940 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mDe-registration finished.
[m2025-09-18 15:45:08.268 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.h.c.t.GraceThreadPool[m [34m[][m - [32m==== 优雅关闭线程池 ====
[m2025-09-18 15:45:08.296 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} closing ...
[m2025-09-18 15:45:08.331 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} closed
[m2025-09-18 15:45:08.331 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} closing ...
[m2025-09-18 15:45:08.341 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} closed
[m2025-09-18 15:45:08.341 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} closing ...
[m2025-09-18 15:45:08.349 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} closed
[m2025-09-18 15:45:08.350 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} closing ...
[m2025-09-18 15:45:08.357 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} closed
[m2025-09-18 15:45:20.611 [36mINFO [m [35m[main][m [36mc.a.n.c.e.SearchableProperties[m [34m[][m - [32mproperties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[m2025-09-18 15:45:20.664 [36mINFO [m [35m[background-preinit][m [36mo.h.v.i.u.Version[m [34m[][m - [32mHV000001: Hibernate Validator 6.2.5.Final
[m2025-09-18 15:45:23.434 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 15:45:23.435 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 15:45:27.002 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-18 15:45:27.010 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-18 15:45:27.015 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-18 15:45:27.017 [36mINFO [m [35m[main][m [36mo.s.c.b.c.PropertySourceBootstrapConfiguration[m [34m[][m - [32mLocated property source: [BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive-druid.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive,default'}]
[m2025-09-18 15:45:27.064 [36mINFO [m [35m[main][m [36mc.h.AppMain[m [34m[][m - [32mThe following 1 profile is active: "druid"
[m2025-09-18 15:45:29.988 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 15:45:29.999 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 15:45:30.102 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 74 ms. Found 0 Elasticsearch repository interfaces.
[m2025-09-18 15:45:30.110 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 15:45:30.112 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 15:45:30.153 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 40 ms. Found 0 Reactive Elasticsearch repository interfaces.
[m2025-09-18 15:45:30.172 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 15:45:30.177 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Redis repositories in DEFAULT mode.
[m2025-09-18 15:45:30.226 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 39 ms. Found 0 Redis repository interfaces.
[m2025-09-18 15:45:30.851 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:45:30.852 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:45:30.852 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:45:30.852 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:45:30.852 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:45:30.853 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:45:30.853 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:45:30.853 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:45:30.853 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:45:30.853 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:45:31.828 [36mINFO [m [35m[main][m [36mo.s.c.c.s.GenericScope[m [34m[][m - [32mBeanFactory id=6a689e8b-b3c8-374c-807c-3d8ec15b808c
[m2025-09-18 15:45:33.366 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:45:33.379 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:45:33.383 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$597/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:45:33.399 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:45:33.689 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'dictProperties' of type [com.hl.dict.config.DictProperties$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:45:33.697 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'dynamicTableNameHandler' of type [com.hl.dict.config.DynamicTableNameHandler$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:45:34.996 [36mINFO [m [35m[main][m [36mo.s.b.w.e.t.TomcatWebServer[m [34m[][m - [32mTomcat initialized with port(s): 28183 (http)
[m2025-09-18 15:45:35.050 [36mINFO [m [35m[main][m [36mo.a.c.h.Http11NioProtocol[m [34m[][m - [32mInitializing ProtocolHandler ["http-nio-28183"]
[m2025-09-18 15:45:35.057 [36mINFO [m [35m[main][m [36mo.a.c.c.StandardService[m [34m[][m - [32mStarting service [Tomcat]
[m2025-09-18 15:45:35.057 [36mINFO [m [35m[main][m [36mo.a.c.c.StandardEngine[m [34m[][m - [32mStarting Servlet engine: [Apache Tomcat/9.0.83]
[m2025-09-18 15:45:35.484 [36mINFO [m [35m[main][m [36mo.a.c.c.C.[.[.[/][m [34m[][m - [32mInitializing Spring embedded WebApplicationContext
[m2025-09-18 15:45:35.484 [36mINFO [m [35m[main][m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[m [34m[][m - [32mRoot WebApplicationContext: initialization completed in 8391 ms
[m2025-09-18 15:45:36.008 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'sso-hl' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 15:45:37.057 [36mINFO [m [35m[main][m [36mc.h.d.c.MybatisPlusEnhancerConfiguration[m [34m[][m - [32m✅ Enhanced MybatisPlusInterceptor with DynamicTableNameInnerInterceptor.
[m2025-09-18 15:45:41.204 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-18 15:45:41.204 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.222 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-18 15:45:41.222 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.239 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-18 15:45:41.239 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.257 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-18 15:45:41.257 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.273 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-18 15:45:41.273 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.292 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-18 15:45:41.293 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.310 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-18 15:45:41.311 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.327 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-18 15:45:41.327 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.343 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-18 15:45:41.343 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.360 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-18 15:45:41.360 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.378 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-18 15:45:41.379 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.397 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-18 15:45:41.398 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.415 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-18 15:45:41.450 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-18 15:45:41.450 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.472 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-18 15:45:41.473 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.487 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-18 15:45:41.489 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.508 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjLzfx".
[m2025-09-18 15:45:41.508 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjLzfx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.525 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-18 15:45:41.526 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.545 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-18 15:45:41.546 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.564 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-18 15:45:41.565 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.586 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-18 15:45:41.586 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.606 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-18 15:45:41.606 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.621 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-18 15:45:41.622 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.637 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-18 15:45:41.638 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.653 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-18 15:45:41.654 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.691 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-18 15:45:41.691 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.718 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-18 15:45:41.719 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.736 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-18 15:45:41.737 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.751 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-18 15:45:41.751 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.780 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-18 15:45:41.780 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.796 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-18 15:45:41.797 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.814 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-18 15:45:41.815 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.831 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-18 15:45:41.831 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.846 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-18 15:45:41.847 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.861 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-18 15:45:41.861 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.878 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-18 15:45:41.878 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.895 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-18 15:45:41.895 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.911 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-18 15:45:41.911 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.924 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-18 15:45:41.924 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.952 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-18 15:45:41.952 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.974 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-18 15:45:41.975 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:41.990 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-18 15:45:41.991 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:42.009 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-18 15:45:42.009 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:42.029 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-18 15:45:42.030 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:42.047 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-18 15:45:42.047 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:42.063 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-18 15:45:42.063 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:42.079 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-18 15:45:42.079 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:42.096 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-18 15:45:42.097 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:42.117 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjYjbb".
[m2025-09-18 15:45:42.117 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjYjbb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:42.136 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-18 15:45:42.136 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:42.165 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-18 15:45:42.165 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:42.196 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-18 15:45:42.196 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:42.225 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-18 15:45:42.226 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:42.243 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-18 15:45:42.243 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:42.261 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-18 15:45:42.261 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:45:43.360 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'hl-task' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 15:45:59.759 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:46:03.309 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:46:03.310 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:46:03.434 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:46:06.473 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:46:06.473 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:46:06.607 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:46:09.653 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:46:09.654 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:46:09.785 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:46:12.816 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:46:12.816 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:46:12.955 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:46:15.989 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:46:15.989 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:46:16.128 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:46:19.166 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:46:19.167 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:46:19.302 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:46:22.344 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:46:22.344 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:46:22.475 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:46:25.504 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:46:25.505 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:46:25.955 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CG9Z9HJ3FJW -> BusinessTripStrategy
[m2025-09-18 15:46:25.956 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: C09ZZEL29S8 -> LeaveChangZhouStrategy
[m2025-09-18 15:46:25.956 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CC42ITDEHRQ -> LeaveRequestStrategy
[m2025-09-18 15:46:25.956 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CKSWWQQWW7H -> OverseasTravelStrategy
[m2025-09-18 15:46:25.956 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CNE2TZD2GTE -> PoliceAbilityStrategy
[m2025-09-18 15:46:25.956 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CXQNJMIAR1C -> PoliceClubActivityStrategy
[m2025-09-18 15:46:25.956 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: C30NIBEJEGI -> PoliceDrinkReportStrategy
[m2025-09-18 15:46:26.169 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:46:29.207 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:46:29.208 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:46:29.518 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:46:32.553 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:46:32.556 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:46:49.259 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 15:46:49.581 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 15:46:49.581 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 15:46:50.628 [36mINFO [m [35m[main][m [36mc.h.n.c.SharedNacosNameSpace[m [34m[][m - [32m{"secretKey":"","password":"hl123","namespace":"public","accessKey":"","serverAddr":"**************:8848","isUseCloudNamespaceParsing":"false","clusterName":"","username":"nacos"}
[m2025-09-18 15:46:51.862 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 15:46:52.633 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'hl-dict' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 15:46:57.827 [36mINFO [m [35m[main][m [36mo.s.b.a.e.w.EndpointLinksResolver[m [34m[][m - [32mExposing 1 endpoint(s) beneath base path '/actuator'
[m2025-09-18 15:46:59.425 [36mINFO [m [35m[main][m [36mc.h.s.c.SecurityConfig[m [34m[][m - [32m[/error/logs, /dict-test/test, /test/dict, /dict/api-query, /dict/add-batch] --> 200
[m2025-09-18 15:46:59.814 [36mINFO [m [35m[main][m [36mo.s.s.w.DefaultSecurityFilterChain[m [34m[][m - [32mWill secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@67c5c1f4, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7778eb84, org.springframework.security.web.context.SecurityContextPersistenceFilter@11bd348e, org.springframework.security.web.header.HeaderWriterFilter@23dde46f, org.springframework.security.web.authentication.logout.LogoutFilter@4b0d888, org.springframework.web.filter.CorsFilter@1982e140, com.hl.security.config.sso.SsoAuthTokenFilter@583b177a, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@595d7318, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@ea6ced7, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@5d4f402d, org.springframework.security.web.session.SessionManagementFilter@cbd38bc, org.springframework.security.web.access.ExceptionTranslationFilter@2c8f9ac4, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@cd661f4]
[m2025-09-18 15:47:07.545 [36mINFO [m [35m[main][m [36mo.a.c.h.Http11NioProtocol[m [34m[][m - [32mStarting ProtocolHandler ["http-nio-28183"]
[m2025-09-18 15:47:07.695 [36mINFO [m [35m[main][m [36mo.s.b.w.e.t.TomcatWebServer[m [34m[][m - [32mTomcat started on port(s): 28183 (http) with context path ''
[m2025-09-18 15:47:08.911 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 15:47:08.917 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 15:47:08.918 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 15:47:09.186 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mnacos registry, default hl-wj-police-archive *************:28183 register finished
[m2025-09-18 15:47:13.763 [36mINFO [m [35m[main][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 15:47:14.168 [36mINFO [m [35m[main][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mCreated new connection: rabbitConnectionFactory#6193f60c:0/SimpleConnection@22f6ece6 [delegate=amqp://admin@**************:5672/, localPort= 61581]
[m2025-09-18 15:47:14.821 [36mINFO [m [35m[main][m [36mc.h.AppMain[m [34m[][m - [32mStarted AppMain in 116.185 seconds (JVM running for 121.371)
[m2025-09-18 15:47:14.823 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 15:47:14.823 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 15:47:18.954 [36mINFO [m [35m[main][m [36mc.h.s.c.s.c.SsoCache[m [34m[][m - [32minit cache_data -> size 21 --> ["user_circle","role","org_job_user","user_resources","project","resources","user_leave","resources_user","user_all","user_role_meet","circle_user","user_role","role_resources","police","role_user","organization","user_org_role","job","circle","user","organization_tree"] -> 
[m2025-09-18 15:47:18.960 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 15:47:18.960 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 15:47:19.235 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive.properties, group=default
[m2025-09-18 15:47:19.236 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive-druid.properties, group=default
[m2025-09-18 15:47:19.236 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive, group=default
[m2025-09-18 15:47:19.313 [36mINFO [m [35m[main][m [36mc.h.d.s.i.DictDataServiceImpl[m [34m[][m - [32mrefresh dict cache 
[m2025-09-18 15:47:20.366 [36mINFO [m [35m[RMI TCP Connection(6)-*************][m [36mo.a.c.c.C.[.[.[/][m [34m[][m - [32mInitializing Spring DispatcherServlet 'dispatcherServlet'
[m2025-09-18 15:47:20.392 [36mINFO [m [35m[RMI TCP Connection(6)-*************][m [36mo.s.w.s.DispatcherServlet[m [34m[][m - [32mInitializing Servlet 'dispatcherServlet'
[m2025-09-18 15:47:20.455 [36mINFO [m [35m[RMI TCP Connection(6)-*************][m [36mo.s.w.s.DispatcherServlet[m [34m[][m - [32mCompleted initialization in 63 ms
[m2025-09-18 15:47:20.733 [36mINFO [m [35m[main][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：SELECT id,dict_type,parent_id,dict_value,dict_name,sort,field_class,remark,create_user,create_time,update_user,update_time,is_disable,tenant_id,is_system,is_default,extend FROM dict_data
[m2025-09-18 15:47:21.471 [33mWARN [m [35m[RMI TCP Connection(5)-*************][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.15]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor143.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-18 15:47:26.891 [31mERROR[m [35m[main][m [36mc.a.d.f.s.StatFilter[m [34m[][m - [31mslow sql 1934 millis. SELECT 1[]
[m2025-09-18 15:47:30.440 [36mINFO [m [35m[main][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} inited
[m2025-09-18 15:47:34.865 [36mINFO [m [35m[RMI TCP Connection(5)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} inited
[m2025-09-18 15:47:39.196 [36mINFO [m [35m[RMI TCP Connection(5)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} inited
[m2025-09-18 15:47:44.599 [36mINFO [m [35m[RMI TCP Connection(5)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} inited
[m2025-09-18 15:47:56.945 [36mINFO [m [35m[http-nio-28183-exec-1][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：select category_name, feature_name from police_capability_eval group by category_name, feature_name
[m2025-09-18 15:47:57.792 [36mINFO [m [35m[http-nio-28183-exec-1][m [36mc.h.l.LogSlf4j[m [34m[][m - [32m127.0.0.1:/capabilityEval/tree (局长指令) ->  -> 756ms -> {"count":0,"data":[{"id":"1","parentId":"0","weight":0,"name":"专业打击"},{"id":"2","parentId":"0","weight":0,"name":"执法监督"},{"id":"3","parentId":"0","weight":0,"name":"情指作战"},{"id":"4","parentId":"0","weight":0,"name":"综合保障"},{"id":"5","parentId":"0","weight":0,"name":"基础防控"},{"id":"6","parentId":"0","weight":0,"name":"交通治理"},{"id":"7","parentId":"0","weight":0,"name":"智慧警务","children":[{"id":"8","parentId":"7","weight":8,"name":"数据建模与实战应用"}]}],"errno":200,"error":"操作成功"}
[m2025-09-18 15:48:59.009 [36mINFO [m [35m[http-nio-28183-exec-4][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：select category_name, feature_name from police_capability_eval group by category_name, feature_name
[m2025-09-18 15:52:17.405 [36mINFO [m [35m[http-nio-28183-exec-4][m [36mc.h.l.LogSlf4j[m [34m[][m - [32m127.0.0.1:/capabilityEval/tree (局长指令) ->  -> 198381ms -> {"count":0,"data":[{"id":"1","parentId":"0","weight":0,"name":"专业打击"},{"id":"2","parentId":"0","weight":0,"name":"执法监督"},{"id":"3","parentId":"0","weight":0,"name":"情指作战"},{"id":"4","parentId":"0","weight":0,"name":"综合保障"},{"id":"5","parentId":"0","weight":0,"name":"基础防控"},{"id":"6","parentId":"0","weight":0,"name":"交通治理"},{"id":"7","parentId":"0","weight":0,"name":"智慧警务","children":[{"id":"8","parentId":"7","weight":8,"name":"数据建模与实战应用"}]}],"errno":200,"error":"操作成功"}
[m2025-09-18 15:52:17.407 [33mWARN [m [35m[AMQP Connection **************:5672][m [36mc.r.c.i.ForgivingExceptionHandler[m [34m[][m - [33mAn unexpected connection driver error occurred (Exception message: Connection reset)
[m2025-09-18 15:52:17.465 [33mWARN [m [35m[Thread-25][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-18 15:52:17.515 [33mWARN [m [35m[Thread-31][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-18 15:52:17.515 [33mWARN [m [35m[Thread-31][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-18 15:52:17.533 [33mWARN [m [35m[Thread-25][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-18 15:52:17.620 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mWaiting for workers to finish.
[m2025-09-18 15:52:17.631 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mSuccessfully waited for workers to finish.
[m2025-09-18 15:52:17.634 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mo.s.b.w.e.t.GracefulShutdown[m [34m[][m - [32mCommencing graceful shutdown. Waiting for active requests to complete
[m2025-09-18 15:52:17.821 [36mINFO [m [35m[tomcat-shutdown][m [36mo.s.b.w.e.t.GracefulShutdown[m [34m[][m - [32mGraceful shutdown complete
[m2025-09-18 15:52:17.904 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mDe-registering from Nacos Server now...
[m2025-09-18 15:52:18.009 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mDe-registration finished.
[m2025-09-18 15:52:18.341 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.h.c.t.GraceThreadPool[m [34m[][m - [32m==== 优雅关闭线程池 ====
[m2025-09-18 15:52:18.364 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} closing ...
[m2025-09-18 15:52:18.402 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} closed
[m2025-09-18 15:52:18.403 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} closing ...
[m2025-09-18 15:52:18.415 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} closed
[m2025-09-18 15:52:18.415 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} closing ...
[m2025-09-18 15:52:18.425 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} closed
[m2025-09-18 15:52:18.425 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} closing ...
[m2025-09-18 15:52:18.433 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} closed
[m2025-09-18 15:56:30.692 [36mINFO [m [35m[main][m [36mc.a.n.c.e.SearchableProperties[m [34m[][m - [32mproperties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
[m2025-09-18 15:56:30.719 [36mINFO [m [35m[background-preinit][m [36mo.h.v.i.u.Version[m [34m[][m - [32mHV000001: Hibernate Validator 6.2.5.Final
[m2025-09-18 15:56:33.643 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 15:56:33.643 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 15:56:35.845 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive] & group[default]
[m2025-09-18 15:56:35.857 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive.properties] & group[default]
[m2025-09-18 15:56:35.861 [33mWARN [m [35m[main][m [36mc.a.c.n.c.NacosPropertySourceBuilder[m [34m[][m - [33mIgnore the empty nacos configuration and get it based on dataId[hl-wj-police-archive-druid.properties] & group[default]
[m2025-09-18 15:56:35.862 [36mINFO [m [35m[main][m [36mo.s.c.b.c.PropertySourceBootstrapConfiguration[m [34m[][m - [32mLocated property source: [BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive-druid.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive.properties,default'}, BootstrapPropertySource {name='bootstrapProperties-hl-wj-police-archive,default'}]
[m2025-09-18 15:56:35.968 [36mINFO [m [35m[main][m [36mc.h.AppMain[m [34m[][m - [32mThe following 1 profile is active: "druid"
[m2025-09-18 15:56:37.657 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 15:56:37.664 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 15:56:37.837 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 160 ms. Found 0 Elasticsearch repository interfaces.
[m2025-09-18 15:56:37.842 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 15:56:37.844 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Reactive Elasticsearch repositories in DEFAULT mode.
[m2025-09-18 15:56:37.985 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 140 ms. Found 0 Reactive Elasticsearch repository interfaces.
[m2025-09-18 15:56:37.999 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mMultiple Spring Data modules found, entering strict repository configuration mode
[m2025-09-18 15:56:38.003 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mBootstrapping Spring Data Redis repositories in DEFAULT mode.
[m2025-09-18 15:56:38.138 [36mINFO [m [35m[main][m [36mo.s.d.r.c.RepositoryConfigurationDelegate[m [34m[][m - [32mFinished Spring Data repository scanning in 127 ms. Found 0 Redis repository interfaces.
[m2025-09-18 15:56:38.637 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeBaseSearchDocumentMapper' and 'com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:56:38.637 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'policeSearchMapper' and 'com.hl.archive.search.mapper.PoliceSearchMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:56:38.637 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:56:38.637 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseInfoTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseInfoTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:56:38.638 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCaseMeasureStatDocumentMapper' and 'com.hl.archive.search.mapper.ViewCaseMeasureStatDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:56:38.638 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewCasePlaceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewCasePlaceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:56:38.638 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExamineTaskDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExamineTaskDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:56:38.638 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceExportDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceExportDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:56:38.638 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewPoliceNotifyDocumentMapper' and 'com.hl.archive.search.mapper.ViewPoliceNotifyDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:56:38.638 [33mWARN [m [35m[main][m [36mo.m.s.m.ClassPathMapperScanner[m [34m[][m - [33mSkipping MapperFactoryBean with name 'viewTaskTimeoutResultDocumentMapper' and 'com.hl.archive.search.mapper.ViewTaskTimeoutResultDocumentMapper' mapperInterface. Bean already defined with the same name!
[m2025-09-18 15:56:39.114 [36mINFO [m [35m[main][m [36mo.s.c.c.s.GenericScope[m [34m[][m - [32mBeanFactory id=6a689e8b-b3c8-374c-807c-3d8ec15b808c
[m2025-09-18 15:56:39.968 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:56:39.976 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:56:39.979 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$510/**********] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:56:39.992 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:56:40.172 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'dictProperties' of type [com.hl.dict.config.DictProperties$$EnhancerBySpringCGLIB$$a522fbc] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:56:40.179 [36mINFO [m [35m[main][m [36mo.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker[m [34m[][m - [32mBean 'dynamicTableNameHandler' of type [com.hl.dict.config.DynamicTableNameHandler$$EnhancerBySpringCGLIB$$67de8dc1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[m2025-09-18 15:56:40.953 [36mINFO [m [35m[main][m [36mo.s.b.w.e.t.TomcatWebServer[m [34m[][m - [32mTomcat initialized with port(s): 28183 (http)
[m2025-09-18 15:56:40.971 [36mINFO [m [35m[main][m [36mo.a.c.h.Http11NioProtocol[m [34m[][m - [32mInitializing ProtocolHandler ["http-nio-28183"]
[m2025-09-18 15:56:40.975 [36mINFO [m [35m[main][m [36mo.a.c.c.StandardService[m [34m[][m - [32mStarting service [Tomcat]
[m2025-09-18 15:56:40.975 [36mINFO [m [35m[main][m [36mo.a.c.c.StandardEngine[m [34m[][m - [32mStarting Servlet engine: [Apache Tomcat/9.0.83]
[m2025-09-18 15:56:41.136 [36mINFO [m [35m[main][m [36mo.a.c.c.C.[.[.[/][m [34m[][m - [32mInitializing Spring embedded WebApplicationContext
[m2025-09-18 15:56:41.136 [36mINFO [m [35m[main][m [36mo.s.b.w.s.c.ServletWebServerApplicationContext[m [34m[][m - [32mRoot WebApplicationContext: initialization completed in 5139 ms
[m2025-09-18 15:56:42.008 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'sso-hl' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 15:56:42.756 [36mINFO [m [35m[main][m [36mc.h.d.c.MybatisPlusEnhancerConfiguration[m [34m[][m - [32m✅ Enhanced MybatisPlusInterceptor with DynamicTableNameInnerInterceptor.
[m2025-09-18 15:56:45.182 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGattxz".
[m2025-09-18 15:56:45.183 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGattxz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.194 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGatwlqk".
[m2025-09-18 15:56:45.194 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGatwlqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.206 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrGrjd".
[m2025-09-18 15:56:45.207 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrGrjd ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.220 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHsjq".
[m2025-09-18 15:56:45.220 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHsjq ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.240 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrHyzk".
[m2025-09-18 15:56:45.240 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrHyzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.251 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrJkzk".
[m2025-09-18 15:56:45.251 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrJkzk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.261 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrPthz".
[m2025-09-18 15:56:45.261 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrPthz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.271 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrTzqk".
[m2025-09-18 15:56:45.272 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrTzqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.281 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrWjdc".
[m2025-09-18 15:56:45.282 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrWjdc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.291 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBrYscg".
[m2025-09-18 15:56:45.291 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBrYscg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.302 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlDwry".
[m2025-09-18 15:56:45.303 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlDwry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.313 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjBzjlGrry".
[m2025-09-18 15:56:45.314 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjBzjlGrry ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.324 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mThis "id" is the table primary key by default name for `id` in Class: "com.hl.orasync.domain.VWjBzjlOld",So @TableField will not work!
[m2025-09-18 15:56:45.341 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjFxyh".
[m2025-09-18 15:56:45.342 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjFxyh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.355 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsb".
[m2025-09-18 15:56:45.355 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.365 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjJcsjScsbWj".
[m2025-09-18 15:56:45.366 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjJcsjScsbWj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.376 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjLzfx".
[m2025-09-18 15:56:45.376 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjLzfx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.388 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjMjqyxxsb".
[m2025-09-18 15:56:45.389 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjMjqyxxsb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.401 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjNlcp".
[m2025-09-18 15:56:45.401 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjNlcp ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.412 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtClxx".
[m2025-09-18 15:56:45.412 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtClxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.424 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtFcqk".
[m2025-09-18 15:56:45.424 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtFcqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.437 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtGpjjtz".
[m2025-09-18 15:56:45.438 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtGpjjtz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.449 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtPosxbzxr".
[m2025-09-18 15:56:45.449 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtPosxbzxr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.459 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjQtQtsx".
[m2025-09-18 15:56:45.459 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjQtQtsx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.469 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRybzjl".
[m2025-09-18 15:56:45.469 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRybzjl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.486 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxxFjxx".
[m2025-09-18 15:56:45.487 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxxFjxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.502 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjbxx".
[m2025-09-18 15:56:45.502 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjbxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.512 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjdkh".
[m2025-09-18 15:56:45.512 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjdkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.522 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjlxx".
[m2025-09-18 15:56:45.522 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjlxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.532 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtcy".
[m2025-09-18 15:56:45.532 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtcy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.541 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjtzz".
[m2025-09-18 15:56:45.542 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjtzz ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.552 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjxxx".
[m2025-09-18 15:56:45.552 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjxxx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.561 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyjyxl".
[m2025-09-18 15:56:45.562 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyjyxl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.571 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyndkh".
[m2025-09-18 15:56:45.571 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyndkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.581 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRytc".
[m2025-09-18 15:56:45.581 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRytc ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.590 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyxlxw".
[m2025-09-18 15:56:45.591 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyxlxw ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.602 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyydkh".
[m2025-09-18 15:56:45.603 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyydkh ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.612 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzwzj".
[m2025-09-18 15:56:45.612 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzwzj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.623 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjRyzzmm".
[m2025-09-18 15:56:45.623 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjRyzzmm ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.638 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjb".
[m2025-09-18 15:56:45.638 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.651 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycl".
[m2025-09-18 15:56:45.651 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.662 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWgwjdjbRycljg".
[m2025-09-18 15:56:45.662 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWgwjdjbRycljg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.673 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjWsks".
[m2025-09-18 15:56:45.673 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjWsks ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.684 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgr".
[m2025-09-18 15:56:45.685 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgr ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.696 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrPylx".
[m2025-09-18 15:56:45.696 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrPylx ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.706 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrTwzl".
[m2025-09-18 15:56:45.706 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrTwzl ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.717 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXhjhRxgrXjsj".
[m2025-09-18 15:56:45.717 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXhjhRxgrXjsj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.726 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjXlda".
[m2025-09-18 15:56:45.727 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjXlda ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.738 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjYjbb".
[m2025-09-18 15:56:45.739 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjYjbb ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.749 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnGwth".
[m2025-09-18 15:56:45.749 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnGwth ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.763 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnJsbqy".
[m2025-09-18 15:56:45.764 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnJsbqy ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.780 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnShkbjg".
[m2025-09-18 15:56:45.780 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnShkbjg ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.796 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnTzgqjj".
[m2025-09-18 15:56:45.796 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnTzgqjj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.808 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnXszj".
[m2025-09-18 15:56:45.808 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnXszj ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:45.829 [33mWARN [m [35m[main][m [36mc.b.m.c.m.TableInfoHelper[m [34m[][m - [33mCan not find table primary key in Class: "com.hl.orasync.domain.VWjZnYjqk".
[m2025-09-18 15:56:45.830 [33mWARN [m [35m[main][m [36mc.b.m.c.i.DefaultSqlInjector[m [34m[][m - [33mclass com.hl.orasync.domain.VWjZnYjqk ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
[m2025-09-18 15:56:46.994 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'hl-task' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 15:56:56.517 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:56:59.779 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:56:59.780 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:56:59.835 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:57:02.874 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:57:02.874 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:57:02.931 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:57:07.971 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:57:07.971 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:57:08.032 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:57:11.071 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:57:11.071 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:57:11.139 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:57:14.163 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:57:14.163 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:57:14.228 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:57:17.262 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:57:17.262 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:57:17.331 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:57:20.367 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:57:20.367 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:57:20.449 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:57:23.486 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:57:23.486 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:57:23.753 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CG9Z9HJ3FJW -> BusinessTripStrategy
[m2025-09-18 15:57:23.754 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: C09ZZEL29S8 -> LeaveChangZhouStrategy
[m2025-09-18 15:57:23.754 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CC42ITDEHRQ -> LeaveRequestStrategy
[m2025-09-18 15:57:23.754 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CKSWWQQWW7H -> OverseasTravelStrategy
[m2025-09-18 15:57:23.754 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CNE2TZD2GTE -> PoliceAbilityStrategy
[m2025-09-18 15:57:23.754 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: CXQNJMIAR1C -> PoliceClubActivityStrategy
[m2025-09-18 15:57:23.755 [36mINFO [m [35m[main][m [36mc.h.a.l.s.TaskHandleStrategyFactory[m [34m[][m - [32m注册任务处理策略: C30NIBEJEGI -> PoliceDrinkReportStrategy
[m2025-09-18 15:57:23.823 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:57:26.844 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:57:26.845 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:57:26.977 [36mINFO [m [35m[main][m [36measy-es[m [34m[][m - [32m===> Not smoothly process index mode activated
[m2025-09-18 15:57:30.005 [31mERROR[m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [31mprocess index exception:,java.util.concurrent.CompletionException: java.lang.ArithmeticException: / by zero
[m2025-09-18 15:57:30.005 [33mWARN [m [35m[ForkJoinPool.commonPool-worker-9][m [36measy-es[m [34m[][m - [33m===> Unfortunately, auto process index by Easy-Es failed, please check your configuration
[m2025-09-18 15:57:39.188 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 15:57:39.431 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 15:57:39.432 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 15:57:40.004 [36mINFO [m [35m[main][m [36mc.h.n.c.SharedNacosNameSpace[m [34m[][m - [32m{"secretKey":"","password":"hl123","namespace":"public","accessKey":"","serverAddr":"**************:8848","isUseCloudNamespaceParsing":"false","clusterName":"","username":"nacos"}
[m2025-09-18 15:57:41.211 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 15:57:42.052 [36mINFO [m [35m[main][m [36mo.s.c.o.FeignClientFactoryBean[m [34m[][m - [32mFor 'hl-dict' URL not provided. Will try picking an instance via load-balancing.
[m2025-09-18 15:57:44.650 [36mINFO [m [35m[main][m [36mo.s.b.a.e.w.EndpointLinksResolver[m [34m[][m - [32mExposing 1 endpoint(s) beneath base path '/actuator'
[m2025-09-18 15:57:45.354 [36mINFO [m [35m[main][m [36mc.h.s.c.SecurityConfig[m [34m[][m - [32m[/dict/add-batch, /dict/api-query, /dict-test/test, /test/dict, /error/logs] --> 200
[m2025-09-18 15:57:45.512 [36mINFO [m [35m[main][m [36mo.s.s.w.DefaultSecurityFilterChain[m [34m[][m - [32mWill secure any request with [org.springframework.security.web.session.DisableEncodeUrlFilter@b88d294, org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@1cd50433, org.springframework.security.web.context.SecurityContextPersistenceFilter@7951a08c, org.springframework.security.web.header.HeaderWriterFilter@5c190662, org.springframework.security.web.authentication.logout.LogoutFilter@15cba6dc, org.springframework.web.filter.CorsFilter@71bbbb9f, com.hl.security.config.sso.SsoAuthTokenFilter@3ee68eb2, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@1e7245da, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@276511ca, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@6fe47be, org.springframework.security.web.session.SessionManagementFilter@36b1b28, org.springframework.security.web.access.ExceptionTranslationFilter@7a7b6556, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@1080234b]
[m2025-09-18 15:57:49.262 [36mINFO [m [35m[main][m [36mo.a.c.h.Http11NioProtocol[m [34m[][m - [32mStarting ProtocolHandler ["http-nio-28183"]
[m2025-09-18 15:57:49.305 [36mINFO [m [35m[main][m [36mo.s.b.w.e.t.TomcatWebServer[m [34m[][m - [32mTomcat started on port(s): 28183 (http) with context path ''
[m2025-09-18 15:57:50.529 [36mINFO [m [35m[main][m [36mo.s.c.c.u.InetUtils[m [34m[][m - [32mCannot determine local hostname
[m2025-09-18 15:57:50.535 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 15:57:50.535 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 15:57:50.769 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mnacos registry, default hl-wj-police-archive *************:28183 register finished
[m2025-09-18 15:57:53.075 [36mINFO [m [35m[main][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mAttempting to connect to: [**************:5672]
[m2025-09-18 15:57:53.222 [36mINFO [m [35m[main][m [36mo.s.a.r.c.CachingConnectionFactory[m [34m[][m - [32mCreated new connection: rabbitConnectionFactory#511ba9c9:0/SimpleConnection@4613274d [delegate=amqp://admin@**************:5672/, localPort= 55176]
[m2025-09-18 15:57:58.203 [36mINFO [m [35m[main][m [36mc.h.AppMain[m [34m[][m - [32mStarted AppMain in 90.256 seconds (JVM running for 91.824)
[m2025-09-18 15:57:58.211 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 15:57:58.211 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 15:58:03.666 [36mINFO [m [35m[main][m [36mc.h.s.c.s.c.SsoCache[m [34m[][m - [32minit cache_data -> size 21 --> ["user_circle","role","org_job_user","user_resources","project","resources","user_leave","resources_user","user_all","user_role_meet","circle_user","user_role","role_resources","police","role_user","organization","user_org_role","job","circle","user","organization_tree"] -> 2025-09-18 15:56:13
[m2025-09-18 15:58:03.670 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
[m2025-09-18 15:58:03.670 [36mINFO [m [35m[main][m [36mc.a.n.p.a.s.c.ClientAuthPluginManager[m [34m[][m - [32m[ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
[m2025-09-18 15:58:04.496 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive.properties, group=default
[m2025-09-18 15:58:04.497 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive-druid.properties, group=default
[m2025-09-18 15:58:04.497 [36mINFO [m [35m[main][m [36mc.a.c.n.r.NacosContextRefresher[m [34m[][m - [32m[Nacos Config] Listening config: dataId=hl-wj-police-archive, group=default
[m2025-09-18 15:58:04.521 [36mINFO [m [35m[main][m [36mc.h.d.s.i.DictDataServiceImpl[m [34m[][m - [32mrefresh dict cache 
[m2025-09-18 15:58:04.867 [36mINFO [m [35m[main][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：SELECT id,dict_type,parent_id,dict_value,dict_name,sort,field_class,remark,create_user,create_time,update_user,update_time,is_disable,tenant_id,is_system,is_default,extend FROM dict_data
[m2025-09-18 15:58:05.316 [36mINFO [m [35m[RMI TCP Connection(23)-*************][m [36mo.a.c.c.C.[.[.[/][m [34m[][m - [32mInitializing Spring DispatcherServlet 'dispatcherServlet'
[m2025-09-18 15:58:05.316 [36mINFO [m [35m[RMI TCP Connection(23)-*************][m [36mo.s.w.s.DispatcherServlet[m [34m[][m - [32mInitializing Servlet 'dispatcherServlet'
[m2025-09-18 15:58:05.319 [36mINFO [m [35m[RMI TCP Connection(23)-*************][m [36mo.s.w.s.DispatcherServlet[m [34m[][m - [32mCompleted initialization in 3 ms
[m2025-09-18 15:58:06.596 [33mWARN [m [35m[RMI TCP Connection(25)-*************][m [36mo.s.b.a.e.ElasticsearchRestClientHealthIndicator[m [34m[][m - [33mElasticsearch health check failed
[m java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.elasticsearch.client.RestClient.extractAndWrapCause(RestClient.java:932) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:300) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.elasticsearch.client.RestClient.performRequest(RestClient.java:288) ~[elasticsearch-rest-client-7.17.15.jar:7.17.28]
	at org.springframework.boot.actuate.elasticsearch.ElasticsearchRestClientHealthIndicator.doHealthCheck(ElasticsearchRestClientHealthIndicator.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.AbstractHealthIndicator.health(AbstractHealthIndicator.java:82) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthIndicator.getHealth(HealthIndicator.java:37) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:94) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.getHealth(HealthEndpoint.java:41) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getLoggedHealth(HealthEndpointSupport.java:172) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:145) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getAggregateContribution(HealthEndpointSupport.java:156) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getContribution(HealthEndpointSupport.java:141) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:110) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpointSupport.getHealth(HealthEndpointSupport.java:81) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:88) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.health.HealthEndpoint.health(HealthEndpoint.java:78) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[?:1.8.0_432-432]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[?:1.8.0_432-432]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at org.springframework.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:282) ~[spring-core-5.3.31.jar:5.3.31]
	at org.springframework.boot.actuate.endpoint.invoke.reflect.ReflectiveOperationInvoker.invoke(ReflectiveOperationInvoker.java:74) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.annotation.AbstractDiscoveredOperation.invoke(AbstractDiscoveredOperation.java:60) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:124) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at org.springframework.boot.actuate.endpoint.jmx.EndpointMBean.invoke(EndpointMBean.java:97) ~[spring-boot-actuator-2.7.18.jar:2.7.18]
	at com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.invoke(DefaultMBeanServerInterceptor.java:819) ~[?:1.8.0_432-432]
	at com.sun.jmx.mbeanserver.JmxMBeanServer.invoke(JmxMBeanServer.java:801) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doOperation(RMIConnectionImpl.java:1468) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.access$300(RMIConnectionImpl.java:76) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl$PrivilegedOperation.run(RMIConnectionImpl.java:1309) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.doPrivilegedOperation(RMIConnectionImpl.java:1401) ~[?:1.8.0_432-432]
	at javax.management.remote.rmi.RMIConnectionImpl.invoke(RMIConnectionImpl.java:829) ~[?:1.8.0_432-432]
	at sun.reflect.GeneratedMethodAccessor69.invoke(Unknown Source) ~[?:?]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[?:1.8.0_432-432]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[?:1.8.0_432-432]
	at sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:357) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:200) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport$1.run(Transport.java:197) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.Transport.serviceCall(Transport.java:196) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:573) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:834) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:688) ~[?:1.8.0_432-432]
	at java.security.AccessController.doPrivileged(Native Method) ~[?:1.8.0_432-432]
	at sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:687) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[?:1.8.0_432-432]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[?:1.8.0_432-432]
	at java.lang.Thread.run(Thread.java:750) ~[?:1.8.0_432-432]
Caused by: java.net.ConnectException: Timeout connecting to [/192.168.10.98:9200]
	at org.apache.http.nio.pool.RouteSpecificPool.timeout(RouteSpecificPool.java:169) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool.requestTimeout(AbstractNIOConnPool.java:630) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.nio.pool.AbstractNIOConnPool$InternalSessionRequestCallback.timeout(AbstractNIOConnPool.java:896) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.SessionRequestImpl.timeout(SessionRequestImpl.java:198) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processTimeouts(DefaultConnectingIOReactor.java:213) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor.processEvents(DefaultConnectingIOReactor.java:158) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.reactor.AbstractMultiworkerIOReactor.execute(AbstractMultiworkerIOReactor.java:351) ~[httpcore-nio-4.4.16.jar:4.4.16]
	at org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager.execute(PoolingNHttpClientConnectionManager.java:221) ~[httpasyncclient-4.1.5.jar:4.1.5]
	at org.apache.http.impl.nio.client.CloseableHttpAsyncClientBase$1.run(CloseableHttpAsyncClientBase.java:64) ~[httpasyncclient-4.1.5.jar:4.1.5]
	... 1 more
2025-09-18 15:58:11.816 [36mINFO [m [35m[main][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} inited
[m2025-09-18 15:58:16.231 [36mINFO [m [35m[RMI TCP Connection(25)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} inited
[m2025-09-18 15:58:17.999 [36mINFO [m [35m[http-nio-28183-exec-1][m [36mc.h.l.c.l.LogSql[m [34m[][m - [32mExecute SQL：select category_name, feature_name from police_capability_eval group by category_name, feature_name
[m2025-09-18 15:58:18.120 [36mINFO [m [35m[http-nio-28183-exec-1][m [36mc.h.l.LogSlf4j[m [34m[][m - [32m127.0.0.1:/capabilityEval/tree (局长指令) ->  -> 47ms -> {"count":0,"data":[{"children":[{"children":[],"id":"2","name":"侦查研判","parentId":"1"},{"children":[],"id":"3","name":"检验鉴定","parentId":"1"},{"children":[],"id":"4","name":"监所安全管理","parentId":"1"},{"children":[],"id":"5","name":"获取线索","parentId":"1"},{"children":[],"id":"6","name":"审讯突破","parentId":"1"},{"children":[],"id":"7","name":"实战指挥","parentId":"1"},{"children":[],"id":"8","name":"现场勘查","parentId":"1"},{"children":[],"id":"9","name":"监所协助破案","parentId":"1"}],"id":"1","name":"专业打击","parentId":"0"},{"children":[{"children":[],"id":"11","name":"案件审核（办理）","parentId":"10"},{"children":[],"id":"12","name":"数字督察","parentId":"10"},{"children":[],"id":"13","name":"执法监督指导","parentId":"10"},{"children":[],"id":"14","name":"现场督察","parentId":"10"},{"children":[],"id":"15","name":"信访举报核查","parentId":"10"},{"children":[],"id":"16","name":"执纪办案","parentId":"10"}],"id":"10","name":"执法监督","parentId":"0"},{"children":[{"children":[],"id":"18","name":"巡防综合能力","parentId":"17"},{"children":[],"id":"19
[m2025-09-18 15:58:20.530 [36mINFO [m [35m[RMI TCP Connection(25)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} inited
[m2025-09-18 15:58:25.977 [36mINFO [m [35m[RMI TCP Connection(25)-*************][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} inited
[m2025-09-18 15:58:35.858 [33mWARN [m [35m[Thread-20][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Start destroying Publisher
[m2025-09-18 15:58:35.858 [33mWARN [m [35m[Thread-14][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Start destroying common HttpClient
[m2025-09-18 15:58:35.858 [33mWARN [m [35m[Thread-20][m [36mc.a.n.c.n.NotifyCenter[m [34m[][m - [33m[NotifyCenter] Destruction of the end
[m2025-09-18 15:58:35.860 [33mWARN [m [35m[Thread-14][m [36mc.a.n.c.h.HttpClientBeanHolder[m [34m[][m - [33m[HttpClientBeanHolder] Destruction of the end
[m2025-09-18 15:58:35.888 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mWaiting for workers to finish.
[m2025-09-18 15:58:35.889 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mo.s.b.w.e.t.GracefulShutdown[m [34m[][m - [32mCommencing graceful shutdown. Waiting for active requests to complete
[m2025-09-18 15:58:36.062 [36mINFO [m [35m[tomcat-shutdown][m [36mo.s.b.w.e.t.GracefulShutdown[m [34m[][m - [32mGraceful shutdown complete
[m2025-09-18 15:58:36.883 [36mINFO [m [35m[org.springframework.amqp.rabbit.RabbitListenerEndpointContainer#0-2][m [36mo.s.a.r.l.SimpleMessageListenerContainer[m [34m[][m - [32mSuccessfully waited for workers to finish.
[m2025-09-18 15:58:36.919 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mDe-registering from Nacos Server now...
[m2025-09-18 15:58:36.922 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.c.n.r.NacosServiceRegistry[m [34m[][m - [32mDe-registration finished.
[m2025-09-18 15:58:37.139 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.h.c.t.GraceThreadPool[m [34m[][m - [32m==== 优雅关闭线程池 ====
[m2025-09-18 15:58:37.150 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} closing ...
[m2025-09-18 15:58:37.167 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-4} closed
[m2025-09-18 15:58:37.167 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} closing ...
[m2025-09-18 15:58:37.174 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-3} closed
[m2025-09-18 15:58:37.174 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} closing ...
[m2025-09-18 15:58:37.180 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-2} closed
[m2025-09-18 15:58:37.180 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} closing ...
[m2025-09-18 15:58:37.186 [36mINFO [m [35m[SpringApplicationShutdownHook][m [36mc.a.d.p.DruidDataSource[m [34m[][m - [32m{dataSource-1} closed
[m