<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceAbilityTagMapper">

  <!-- 统计能力标签的人数分布 -->
  <select id="getAbilityTagStatistics" resultType="java.util.Map">
    SELECT
      ability_tag_code,
      ability_tag_name,
      COUNT(*) as count
    FROM police_ability_tag
    WHERE is_deleted = 0
      <!-- 组织ID处理 -->
      <choose>
        <when test="request.organizationId != null and request.organizationId == '320412000000'">
          AND organization_id = '320412000000'
        </when>
        <when test="request.organizationId != null and request.organizationId != ''">
          AND organization_id LIKE CONCAT(SUBSTRING(#{request.organizationId}, 1, 8), '%')
        </when>
      </choose>

      <!-- 其他筛选条件 -->
      <if test="request.abilityTagCode != null and request.abilityTagCode != ''">
        AND ability_tag_code = #{request.abilityTagCode}
      </if>
      <if test="request.abilityTagName != null and request.abilityTagName != ''">
        AND ability_tag_name LIKE CONCAT('%', #{request.abilityTagName}, '%')
      </if>

      <!-- 过滤空值 -->
      AND ability_tag_code IS NOT NULL
      AND ability_tag_code != ''

    GROUP BY ability_tag_code, ability_tag_name
    ORDER BY COUNT(*) DESC
  </select>

</mapper>