<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceExamRecordMapper">
  <resultMap id="BaseResultMap" type="com.hl.archive.domain.entity.PoliceExamRecord">
    <!--@mbg.generated-->
    <!--@Table police_exam_record-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="id_card" jdbcType="VARCHAR" property="idCard" />
    <result column="organization_id" jdbcType="VARCHAR" property="organizationId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="exam_time" jdbcType="TIMESTAMP" property="examTime" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="grades" jdbcType="VARCHAR" property="grades" />
    <result column="pass_status" jdbcType="VARCHAR" property="passStatus" />
    <result column="is_deleted" jdbcType="BOOLEAN" property="isDeleted" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, id_card, organization_id, title, exam_time, content, grades, pass_status, is_deleted, 
    create_at, update_at, created_by, updated_by
  </sql>
</mapper>