package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.dto.CapabilityEvalStatisticsDTO;
import com.hl.archive.domain.dto.CapabilityEvalStatisticsQueryDTO;
import com.hl.archive.domain.dto.PoliceCapabilityEvalRequestDTO;
import com.hl.archive.domain.entity.PoliceCapabilityEval;
import com.hl.archive.mapper.PoliceCapabilityEvalMapper;
import com.hl.archive.utils.SsoCacheUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PoliceCapabilityEvalService extends ServiceImpl<PoliceCapabilityEvalMapper, PoliceCapabilityEval> {

    private final PoliceCapabilityEvalMapper policeCapabilityEvalMapper;

    /**
     * 简单的树节点类，替代Hutool的Tree
     */
    @Setter
    @Getter
    public static class SimpleTreeNode {
        // Getters and Setters
        private String id;
        private String parentId;
        private String name;
        private List<SimpleTreeNode> children;

        public SimpleTreeNode() {
            this.children = new ArrayList<>();
        }

        public SimpleTreeNode(String id, String parentId, String name) {
            this.id = id;
            this.parentId = parentId;
            this.name = name;
            this.children = new ArrayList<>();
        }

    }

    public Page<PoliceCapabilityEval> pageList(PoliceCapabilityEvalRequestDTO requestDTO) {
        LambdaQueryWrapper<PoliceCapabilityEval> queryWrapper = Wrappers.<PoliceCapabilityEval>lambdaQuery()
                .eq(StringUtils.hasText(requestDTO.getPoliceNumber()), PoliceCapabilityEval::getPoliceNumber, requestDTO.getPoliceNumber());

        if (StringUtils.hasText(requestDTO.getOrganizationId())) {
            if ("32041200000".equals(requestDTO.getOrganizationId())) {
                queryWrapper.eq(PoliceCapabilityEval::getOrganizationId, requestDTO.getOrganizationId());
            } else {
                queryWrapper.like(PoliceCapabilityEval::getOrganizationId, requestDTO.getOrganizationId().substring(0, 8));
            }
        }
        if (StringUtils.hasText(requestDTO.getQuery())) {
            queryWrapper.and(w -> w.like(PoliceCapabilityEval::getParticipantName, requestDTO.getQuery())
                    .or()
                    .like(PoliceCapabilityEval::getPoliceNumber, requestDTO.getQuery())
                    .or()
                    .like(PoliceCapabilityEval::getPlanName, requestDTO.getQuery())
                    .or()
                    .like(PoliceCapabilityEval::getFeatureName, requestDTO.getQuery()));
        }
        queryWrapper.eq(StrUtil.isNotBlank(requestDTO.getFeatureName()), PoliceCapabilityEval::getFeatureName, requestDTO.getFeatureName())
                .eq(StrUtil.isNotBlank(requestDTO.getCategoryName()), PoliceCapabilityEval::getCategoryName, requestDTO.getCategoryName());
        Page<PoliceCapabilityEval> page = this.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), queryWrapper);
        return page;
    }


    public void cleanOrganizationId() {
        this.list().forEach(item -> {
            item.setOrganizationId(SsoCacheUtil.getUserOrgIdByPoliceId(item.getPoliceNumber()));
            this.updateById(item);
        });
    }

    /**
     * 根据 categoryName 和 featureName 进行统计（数据库层统计）
     */
    public List<CapabilityEvalStatisticsDTO> getCapabilityEvalStatistics(CapabilityEvalStatisticsQueryDTO request) {
        // 调用数据库层统计查询
        List<Map<String, Object>> statisticsData = policeCapabilityEvalMapper.getCapabilityEvalStatistics(request);

        // 使用 LinkedHashMap 保持插入顺序
        Map<String, CapabilityEvalStatisticsDTO> categoryMap = new LinkedHashMap<>();

        // 处理查询结果
        for (Map<String, Object> row : statisticsData) {
            String categoryName = (String) row.get("category_name");
            String featureName = (String) row.get("feature_name");
            Integer count = ((Number) row.get("count")).intValue();

            // 获取或创建大类统计对象
            CapabilityEvalStatisticsDTO categoryStatistics = categoryMap.get(categoryName);
            if (categoryStatistics == null) {
                categoryStatistics = new CapabilityEvalStatisticsDTO();
                categoryStatistics.setCategoryName(categoryName);
                categoryStatistics.setCategoryTotalCount(0);
                categoryStatistics.setFeatureStatistics(new ArrayList<>());
                categoryMap.put(categoryName, categoryStatistics);
            }

            // 累加大类总人数
            categoryStatistics.setCategoryTotalCount(categoryStatistics.getCategoryTotalCount() + count);

            // 创建特征统计对象
            CapabilityEvalStatisticsDTO.FeatureStatisticsDTO featureStatistics =
                    new CapabilityEvalStatisticsDTO.FeatureStatisticsDTO();
            featureStatistics.setFeatureName(featureName);
            featureStatistics.setFeatureCount(count);

            categoryStatistics.getFeatureStatistics().add(featureStatistics);
        }

        // 转换为列表并排序
        List<CapabilityEvalStatisticsDTO> resultList = new ArrayList<>(categoryMap.values());

        // 按大类总人数降序排序
        resultList.sort((a, b) -> b.getCategoryTotalCount().compareTo(a.getCategoryTotalCount()));

        // 对每个大类内的特征按人数降序排序（SQL已经排序，这里是保险）
        for (CapabilityEvalStatisticsDTO categoryStatistics : resultList) {
            categoryStatistics.getFeatureStatistics().sort((a, b) ->
                    b.getFeatureCount().compareTo(a.getFeatureCount()));
        }

        return resultList;
    }

    /**
     * @return 树形结构列表
     */
    public List<SimpleTreeNode> getTree() {
        // 获取原始数据
        List<JSONObject> list = policeCapabilityEvalMapper.getTree();

        // 按category_name分组
        Map<String, List<JSONObject>> categoryMap = list.stream()
                .collect(Collectors.groupingBy(a -> a.getString("category_name")));

        // 构建树形结构
        List<SimpleTreeNode> rootNodes = new ArrayList<>();
        int nodeIdCounter = 1;

        for (Map.Entry<String, List<JSONObject>> entry : categoryMap.entrySet()) {
            String categoryName = entry.getKey();
            String categoryId = String.valueOf(nodeIdCounter++);

            // 创建分类节点（父节点）
            SimpleTreeNode categoryNode = new SimpleTreeNode(categoryId, "0", categoryName);

            // 添加特征节点（子节点）
            List<JSONObject> features = entry.getValue();
            for (JSONObject feature : features) {
                String featureName = feature.getString("feature_name");
                String featureId = String.valueOf(nodeIdCounter++);

                SimpleTreeNode featureNode = new SimpleTreeNode(featureId, categoryId, featureName);
                categoryNode.getChildren().add(featureNode);
            }

            rootNodes.add(categoryNode);
        }

        return rootNodes;
    }
}
