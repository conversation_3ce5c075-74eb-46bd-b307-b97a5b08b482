package com.hl.archive.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.constant.ProjectCommonConstants;
import com.hl.archive.domain.dto.PoliceExamRecordRequestDTO;
import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.entity.PoliceExamRecord;
import com.hl.archive.mapper.PoliceExamRecordMapper;
@Service
public class PoliceExamRecordService extends ServiceImpl<PoliceExamRecordMapper, PoliceExamRecord> {

    public Page<PoliceExamRecord> pageList(PoliceExamRecordRequestDTO requestDTO) {
        LambdaQueryWrapper<PoliceExamRecord> queryWrapper = Wrappers.<PoliceExamRecord>lambdaQuery()
                .eq(StrUtil.isNotBlank(requestDTO.getIdCard()), PoliceExamRecord::getIdCard, requestDTO.getIdCard())
                .like(StrUtil.isNotBlank(requestDTO.getTitle()), PoliceExamRecord::getTitle, requestDTO.getTitle());
        if (StrUtil.isNotBlank(requestDTO.getOrganizationId())){
            if (!ProjectCommonConstants.WU_JIN_ORG_CODE.equals(requestDTO.getOrganizationId())) {
                queryWrapper.like(PoliceExamRecord::getOrganizationId, requestDTO.getOrganizationId().substring(0, 8));
            }else {
                queryWrapper.eq(PoliceExamRecord::getOrganizationId, requestDTO.getOrganizationId());
            }
        }
        Page<PoliceExamRecord> page = this.page(Page.of(requestDTO.getPage(), requestDTO.getLimit()), queryWrapper);
        return page;

    }
}
