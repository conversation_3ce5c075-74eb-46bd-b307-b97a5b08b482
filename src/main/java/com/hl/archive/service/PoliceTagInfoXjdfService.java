package com.hl.archive.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.constant.ProjectCommonConstants;
import com.hl.archive.domain.dto.PoliceTagInfoXjdfAddDTO;
import com.hl.archive.domain.dto.PoliceTagInfoXjdfQueryDTO;
import com.hl.archive.domain.dto.PoliceTagInfoXjdfReturnDTO;
import com.hl.archive.domain.dto.PoliceTagInfoXjdfUpdateDTO;
import com.hl.archive.domain.entity.PoliceTagInfoXjdf;
import com.hl.archive.mapper.PoliceTagInfoXjdfMapper;
import com.hl.archive.utils.SsoCacheUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class PoliceTagInfoXjdfService extends ServiceImpl<PoliceTagInfoXjdfMapper, PoliceTagInfoXjdf> {

    public boolean addTagData(PoliceTagInfoXjdfAddDTO dto) {

        List<String> idCardList = dto.getIdCardList();

        List<PoliceTagInfoXjdf> tagInfoDfList = new ArrayList<>();
        for (String s : idCardList) {
            PoliceTagInfoXjdf tagInfoDf = new PoliceTagInfoXjdf();
            tagInfoDf.setIdCard(s);
            String organizationId = SsoCacheUtil.getUserOrgIdByIdCard(s);
            tagInfoDf.setOrganizationId(organizationId);
            tagInfoDf.setTagType(dto.getTagType());
            tagInfoDf.setTagName(dto.getTagName());
            tagInfoDf.setAwardDate(dto.getAwardDate());
            tagInfoDf.setRemark(dto.getRemark());
            if (dto.getFiles() != null && !dto.getFiles().isEmpty()) {
                tagInfoDf.setFiles(dto.getFiles());
            }
            tagInfoDfList.add(tagInfoDf);
        }
        boolean b = this.saveBatch(tagInfoDfList);
        return b;
    }

    public Page<PoliceTagInfoXjdfReturnDTO> listTag(PoliceTagInfoXjdfQueryDTO dto) {
        Page<PoliceTagInfoXjdfReturnDTO> objectPage = Page.of(dto.getPage(), dto.getLimit());
        String organizationId = dto.getOrganizationId();
        if (StrUtil.isNotBlank(organizationId)) {
            if (ProjectCommonConstants.WU_JIN_ORG_CODE.equals(organizationId)) {
                dto.setOrganizationId(null);
            } else {
                dto.setOrganizationId(organizationId.substring(0, 8));
            }
        }
        Page<PoliceTagInfoXjdfReturnDTO> page = this.baseMapper.listTag(objectPage, dto);
        return page;
    }

    /**
     * 修改标签数据（支持人员增减）
     *
     * @param dto 修改数据DTO
     * @return 操作结果
     */
    public boolean updateTagData(PoliceTagInfoXjdfUpdateDTO dto) {
        if (dto == null) {
            throw new IllegalArgumentException("修改参数不能为空");
        }

        // 验证操作类型
        String operation = StrUtil.isBlank(dto.getOperation()) ? PoliceTagInfoXjdfUpdateDTO.Operation.UPDATE : dto.getOperation();

        if (PoliceTagInfoXjdfUpdateDTO.Operation.DELETE.equals(operation)) {
            // 删除整个标签组
            return deleteTagGroup(dto);
        } else {
            // 修改标签组（包括人员增减）
            return updateTagGroup(dto);
        }
    }

    /**
     * 删除整个标签组
     */
    private boolean deleteTagGroup(PoliceTagInfoXjdfUpdateDTO dto) {
        // 构建查询条件
        LambdaQueryWrapper<PoliceTagInfoXjdf> queryWrapper = buildQueryWrapper(dto);

        // 查询要删除的记录
        List<PoliceTagInfoXjdf> existingRecords = this.list(queryWrapper);
        if (existingRecords.isEmpty()) {
            throw new IllegalArgumentException("未找到要删除的标签记录");
        }

        // 批量删除
        List<Long> ids = existingRecords.stream().map(PoliceTagInfoXjdf::getId).collect(Collectors.toList());
        return this.removeBatchByIds(ids);
    }

    /**
     * 修改标签组（包括人员增减）
     */
    private boolean updateTagGroup(PoliceTagInfoXjdfUpdateDTO dto) {
        // 构建查询条件
        LambdaQueryWrapper<PoliceTagInfoXjdf> queryWrapper = buildQueryWrapper(dto);

        // 查询现有记录
        List<PoliceTagInfoXjdf> existingRecords = this.list(queryWrapper);

        boolean success = true;

        // 1. 移除指定人员
        if (CollectionUtil.isNotEmpty(dto.getRemoveIdCardList())) {
            List<Long> removeIds = existingRecords.stream()
                    .filter(record -> dto.getRemoveIdCardList().contains(record.getIdCard()))
                    .map(PoliceTagInfoXjdf::getId)
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(removeIds)) {
                success = this.removeBatchByIds(removeIds);
                // 从现有记录中移除已删除的记录
                existingRecords = existingRecords.stream()
                        .filter(record -> !removeIds.contains(record.getId()))
                        .collect(Collectors.toList());
            }
        }

        // 2. 更新现有记录的标签信息
        if (CollectionUtil.isNotEmpty(existingRecords)) {
            List<PoliceTagInfoXjdf> updateList = new ArrayList<>();
            for (PoliceTagInfoXjdf record : existingRecords) {
                boolean needUpdate = false;

                if (StrUtil.isNotBlank(dto.getTagType()) && !dto.getTagType().equals(record.getTagType())) {
                    record.setTagType(dto.getTagType());
                    needUpdate = true;
                }
                if (StrUtil.isNotBlank(dto.getTagName()) && !dto.getTagName().equals(record.getTagName())) {
                    record.setTagName(dto.getTagName());
                    needUpdate = true;
                }
                if (dto.getAwardDate() != null && !dto.getAwardDate().equals(record.getAwardDate())) {
                    record.setAwardDate(dto.getAwardDate());
                    needUpdate = true;
                }
                if (StrUtil.isNotBlank(dto.getRemark()) && !dto.getRemark().equals(record.getRemark())) {
                    record.setRemark(dto.getRemark());
                    needUpdate = true;
                }
                if (dto.getFiles() != null && !dto.getFiles().equals(record.getFiles()) && !dto.getFiles().isEmpty()) {
                    record.setFiles(dto.getFiles());
                    needUpdate = true;
                }

                if (needUpdate) {
                    updateList.add(record);
                }
            }

            if (CollectionUtil.isNotEmpty(updateList)) {
                success = success && this.updateBatchById(updateList);
            }
        }

        // 3. 添加新人员
        if (CollectionUtil.isNotEmpty(dto.getAddIdCardList())) {
            // 获取现有人员身份证号，避免重复添加
            List<String> existingIdCards = existingRecords.stream()
                    .map(PoliceTagInfoXjdf::getIdCard)
                    .collect(Collectors.toList());

            List<String> newIdCards = dto.getAddIdCardList().stream()
                    .filter(idCard -> !existingIdCards.contains(idCard))
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(newIdCards)) {
                List<PoliceTagInfoXjdf> newRecords = new ArrayList<>();
                for (String idCard : newIdCards) {
                    PoliceTagInfoXjdf newRecord = new PoliceTagInfoXjdf();
                    newRecord.setIdCard(idCard);
                    String organizationId = SsoCacheUtil.getUserOrgIdByIdCard(idCard);
                    newRecord.setOrganizationId(organizationId);

                    // 使用新的标签信息，如果没有则使用原始信息
                    newRecord.setTagType(StrUtil.isNotBlank(dto.getTagType()) ? dto.getTagType() : dto.getOriginalTagType());
                    newRecord.setTagName(StrUtil.isNotBlank(dto.getTagName()) ? dto.getTagName() : dto.getOriginalTagName());
                    newRecord.setAwardDate(dto.getAwardDate() != null ? dto.getAwardDate() : dto.getOriginalAwardDate());
                    newRecord.setRemark(StrUtil.isNotBlank(dto.getRemark()) ? dto.getRemark() : dto.getOriginalRemark());

                    newRecords.add(newRecord);
                }

                success = success && this.saveBatch(newRecords);
            }
        }

        return success;
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<PoliceTagInfoXjdf> buildQueryWrapper(PoliceTagInfoXjdfUpdateDTO dto) {
        LambdaQueryWrapper<PoliceTagInfoXjdf> queryWrapper = Wrappers.<PoliceTagInfoXjdf>lambdaQuery();

        if (StrUtil.isNotBlank(dto.getOriginalTagType())) {
            queryWrapper.eq(PoliceTagInfoXjdf::getTagType, dto.getOriginalTagType());
        }
        if (StrUtil.isNotBlank(dto.getOriginalTagName())) {
            queryWrapper.eq(PoliceTagInfoXjdf::getTagName, dto.getOriginalTagName());
        }
        if (dto.getOriginalAwardDate() != null) {
            queryWrapper.eq(PoliceTagInfoXjdf::getAwardDate, dto.getOriginalAwardDate());
        }
        if (StrUtil.isNotBlank(dto.getOriginalRemark())) {
            queryWrapper.eq(PoliceTagInfoXjdf::getRemark, dto.getOriginalRemark());
        } else {
            queryWrapper.isNull(PoliceTagInfoXjdf::getRemark);
        }

        return queryWrapper;
    }
}
