package com.hl.archive.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hl.archive.domain.dto.AbilityTagStatisticsQueryDTO;
import com.hl.archive.domain.entity.PoliceAbilityTag;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface PoliceAbilityTagMapper extends BaseMapper<PoliceAbilityTag> {

    /**
     * 统计能力标签的人数分布（按字典代码分组）
     */
    List<Map<String, Object>> getAbilityTagStatistics(@Param("request") AbilityTagStatisticsQueryDTO request);
}