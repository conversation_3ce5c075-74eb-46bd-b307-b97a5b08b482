package com.hl.archive.mapper;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hl.archive.domain.dto.CapabilityEvalStatisticsQueryDTO;
import com.hl.archive.domain.entity.PoliceCapabilityEval;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface PoliceCapabilityEvalMapper extends BaseMapper<PoliceCapabilityEval> {

    /**
     * 统计大类和特征的人数分布
     */
    List<Map<String, Object>> getCapabilityEvalStatistics(@Param("request") CapabilityEvalStatisticsQueryDTO request);

    @Select("select category_name, feature_name from police_capability_eval group by category_name, feature_name")
    List<JSONObject> getTree();
}