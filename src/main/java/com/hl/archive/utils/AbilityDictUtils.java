package com.hl.archive.utils;

import com.alibaba.fastjson2.JSONObject;
import com.hl.dict.DictCache;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * 能力标签字典工具类
 * 用于处理从最底层字典代码追溯到顶层 ability_label 的层级关系
 */
@Slf4j
public class AbilityDictUtils {

    private static final String ABILITY_LABEL_ROOT = "ability_label";

    /**
     * 根据字典代码获取完整的层级路径
     * @param dictCode 字典代码（最底层）
     * @return 层级路径列表，从顶层到底层
     */
    public static List<DictLevel> getDictLevelPath(String dictCode) {
        List<DictLevel> path = new ArrayList<>();
        
        try {
            // 从 DictCache.ID_MAP_TREE 中查找字典信息
            JSONObject dictInfo = DictCache.ID_MAP_TREE.getJSONObject(dictCode);
            if (dictInfo == null) {
                log.warn("未找到字典代码: {}", dictCode);
                return path;
            }

            // 递归向上查找父级字典，构建完整路径
            buildPathToRoot(dictInfo, path);
            
            // 反转路径，使其从顶层到底层
            List<DictLevel> reversedPath = new ArrayList<>();
            for (int i = path.size() - 1; i >= 0; i--) {
                reversedPath.add(path.get(i));
            }
            
            return reversedPath;
        } catch (Exception e) {
            log.error("获取字典层级路径失败, dictCode: {}", dictCode, e);
            return path;
        }
    }

    /**
     * 递归构建到根节点的路径
     */
    private static void buildPathToRoot(JSONObject dictInfo, List<DictLevel> path) {
        if (dictInfo == null) {
            return;
        }

        String id = dictInfo.getString("id");
        String name = dictInfo.getString("name");
        String parentId = dictInfo.getString("parent_id");
        
        // 添加当前层级到路径
        DictLevel level = new DictLevel();
        level.setId(id);
        level.setName(name);
        level.setParentId(parentId);
        path.add(level);

        // 如果有父级且不是根节点，继续向上查找
        if (parentId != null && !ABILITY_LABEL_ROOT.equals(id)) {
            JSONObject parentDict = DictCache.ID_MAP_TREE.getJSONObject(parentId);
            buildPathToRoot(parentDict, path);
        }
    }

    /**
     * 获取指定层级的字典名称
     * @param dictCode 字典代码
     * @param level 层级（0=顶层，1=一级，2=二级...）
     * @return 指定层级的字典名称
     */
    public static String getDictNameByLevel(String dictCode, int level) {
        List<DictLevel> path = getDictLevelPath(dictCode);
        if (path.size() > level) {
            return path.get(level).getName();
        }
        return null;
    }

    /**
     * 获取完整的层级名称路径（排除根节点 ability_label）
     * @param dictCode 字典代码
     * @return 层级名称列表，从一级分类到最底层
     */
    public static List<String> getLevelNamePath(String dictCode) {
        List<DictLevel> path = getDictLevelPath(dictCode);
        List<String> nameList = new ArrayList<>();

        // 跳过根节点（ability_label），从第一级分类开始
        for (int i = 1; i < path.size(); i++) {
            nameList.add(path.get(i).getName());
        }

        return nameList;
    }

    /**
     * 获取层级深度（排除根节点）
     * @param dictCode 字典代码
     * @return 层级深度，1=一级分类，2=二级分类...
     */
    public static int getLevelDepth(String dictCode) {
        List<DictLevel> path = getDictLevelPath(dictCode);
        // 减1是因为要排除根节点 ability_label
        return Math.max(0, path.size() - 1);
    }

    /**
     * 检查层级路径是否匹配筛选条件
     * @param dictCode 字典代码
     * @param filterPath 筛选路径
     * @return 是否匹配
     */
    public static boolean matchesLevelPath(String dictCode, List<String> filterPath) {
        if (filterPath == null || filterPath.isEmpty()) {
            return true;
        }

        List<String> actualPath = getLevelNamePath(dictCode);

        // 检查实际路径是否以筛选路径开头
        if (actualPath.size() < filterPath.size()) {
            return false;
        }

        for (int i = 0; i < filterPath.size(); i++) {
            if (!filterPath.get(i).equals(actualPath.get(i))) {
                return false;
            }
        }

        return true;
    }

    /**
     * 字典层级信息
     */
    @Setter
    @Getter
    public static class DictLevel {
        private String id;
        private String name;
        private String parentId;

        @Override
        public String toString() {
            return "DictLevel{" +
                    "id='" + id + '\'' +
                    ", name='" + name + '\'' +
                    ", parentId='" + parentId + '\'' +
                    '}';
        }
    }
}
