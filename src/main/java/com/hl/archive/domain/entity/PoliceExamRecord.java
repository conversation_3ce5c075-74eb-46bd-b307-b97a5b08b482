package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 业务考试模块表
 */
@ApiModel(description = "业务考试模块表")
@Data
@TableName(value = "police_exam_record")
public class PoliceExamRecord {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 单位id
     */
    @TableField(value = "organization_id")
    @ApiModelProperty(value = "单位id")
    private String organizationId;

    /**
     * 考试标题
     */
    @TableField(value = "title")
    @ApiModelProperty(value = "考试标题")
    private String title;

    /**
     * 考试时间
     */
    @TableField(value = "exam_time")
    @ApiModelProperty(value = "考试时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime examTime;

    /**
     * 考试内容
     */
    @TableField(value = "content")
    @ApiModelProperty(value = "考试内容")
    private String content;

    /**
     * 成绩
     */
    @TableField(value = "grades")
    @ApiModelProperty(value = "成绩")
    private String grades;

    /**
     * 合格状态
     */
    @TableField(value = "pass_status")
    @ApiModelProperty(value = "合格状态")
    private String passStatus;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Boolean isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "create_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createAt;

    /**
     * 更新时间
     */
    @TableField(value = "update_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateAt;

    /**
     * 创建人
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人")
    private String updatedBy;
}