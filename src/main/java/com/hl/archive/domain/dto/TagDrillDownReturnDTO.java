package com.hl.archive.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDate;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(description = "标签穿透查询返回DTO")
public class TagDrillDownReturnDTO extends PoliceBasicInfo {

    /**
     * 该人员的标签详情列表
     */
    @ApiModelProperty(value = "该人员的标签详情列表")
    private List<TagDetail> tagDetails;

    /**
     * 标签详情
     */
    @Data
    @ApiModel(description = "标签详情")
    public static class TagDetail {

        /**
         * 标签类型代码
         */
        @ApiModelProperty(value = "标签类型代码")
        private String tagType;

        /**
         * 标签类型名称
         */
        @ApiModelProperty(value = "标签类型名称")
        private String tagTypeName;

        /**
         * 标签名称
         */
        @ApiModelProperty(value = "标签名称")
        private String tagName;

        /**
         * 获得时间
         */
        @ApiModelProperty(value = "获得时间")
        @JsonFormat(pattern = "yyyy-MM-dd")
        private LocalDate awardDate;

        /**
         * 说明
         */
        @ApiModelProperty(value = "说明")
        private String remark;
    }
}
