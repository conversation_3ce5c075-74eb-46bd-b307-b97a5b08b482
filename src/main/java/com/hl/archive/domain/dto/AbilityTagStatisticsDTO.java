package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 能力标签统计返回DTO（动态层级结构）
 */
@Data
@ApiModel(description = "能力标签统计返回DTO")
public class AbilityTagStatisticsDTO {

    /**
     * 当前层级名称
     */
    @ApiModelProperty(value = "当前层级名称")
    private String categoryName;

    /**
     * 当前层级人数
     */
    @ApiModelProperty(value = "当前层级人数")
    private Integer count = 0;

    /**
     * 当前层级深度（0=根层级，1=一级分类，2=二级分类...）
     */
    @ApiModelProperty(value = "当前层级深度")
    private Integer level = 0;

    /**
     * 原始字典代码（叶子节点才有）
     */
    @ApiModelProperty(value = "原始字典代码")
    private String dictCode;

    /**
     * 完整层级路径（从根到当前层级）
     */
    @ApiModelProperty(value = "完整层级路径")
    private List<String> levelPath;

    /**
     * 子层级统计列表
     */
    @ApiModelProperty(value = "子层级统计列表")
    private List<AbilityTagStatisticsDTO> children;

    /**
     * 是否为叶子节点（最底层）
     */
    @ApiModelProperty(value = "是否为叶子节点")
    private Boolean isLeaf = false;
}
