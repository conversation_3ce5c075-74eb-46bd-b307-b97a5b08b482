package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 能力参评统计返回DTO
 */
@Data
@ApiModel(description = "能力参评统计返回DTO")
public class CapabilityEvalStatisticsDTO {

    /**
     * 大类名称
     */
    @ApiModelProperty(value = "大类名称")
    private String categoryName;

    /**
     * 大类总人数
     */
    @ApiModelProperty(value = "大类总人数")
    private Integer categoryTotalCount = 0;

    /**
     * 特征统计详情列表
     */
    @ApiModelProperty(value = "特征统计详情列表")
    private List<FeatureStatisticsDTO> featureStatistics;

    /**
     * 特征统计详情
     */
    @Data
    @ApiModel(description = "特征统计详情")
    public static class FeatureStatisticsDTO {
        
        /**
         * 特征名称
         */
        @ApiModelProperty(value = "特征名称")
        private String featureName;

        /**
         * 特征人数
         */
        @ApiModelProperty(value = "特征人数")
        private Integer featureCount = 0;
    }
}
