package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 能力参评统计查询请求DTO
 */
@Data
@ApiModel(description = "能力参评统计查询请求DTO")
public class CapabilityEvalStatisticsQueryDTO {

    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    /**
     * 大类名称（可选，用于筛选特定大类）
     */
    @ApiModelProperty(value = "大类名称")
    private String categoryName;

    /**
     * 特征名称（可选，用于筛选特定特征）
     */
    @ApiModelProperty(value = "特征名称")
    private String featureName;

    /**
     * 参评状态（可选，用于筛选特定状态）
     */
    @ApiModelProperty(value = "参评状态")
    private String evalStatus;

    /**
     * 审核结果（可选，用于筛选特定审核结果）
     */
    @ApiModelProperty(value = "审核结果")
    private String reviewResult;
}