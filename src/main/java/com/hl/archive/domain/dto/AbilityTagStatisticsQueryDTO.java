package com.hl.archive.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 能力标签统计查询请求DTO（支持动态层级）
 */
@Data
@ApiModel(description = "能力标签统计查询请求DTO")
public class AbilityTagStatisticsQueryDTO {

    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    private String organizationId;

    /**
     * 层级路径筛选（可选，用于筛选特定层级路径下的数据）
     * 例如：["执法能力", "案件办理"] 表示筛选执法能力->案件办理下的数据
     */
    @ApiModelProperty(value = "层级路径筛选")
    private List<String> levelPath;

    /**
     * 指定层级名称筛选（可选，用于筛选包含特定名称的层级）
     */
    @ApiModelProperty(value = "指定层级名称筛选")
    private String categoryName;

    /**
     * 能力标签代码（可选，用于筛选特定标签代码）
     */
    @ApiModelProperty(value = "能力标签代码")
    private String abilityTagCode;

    /**
     * 能力标签名称（可选，用于筛选特定标签名称）
     */
    @ApiModelProperty(value = "能力标签名称")
    private String abilityTagName;

    /**
     * 最大展示层级深度（可选，用于控制返回的层级深度，默认展示所有层级）
     */
    @ApiModelProperty(value = "最大展示层级深度")
    private Integer maxLevel;
}
