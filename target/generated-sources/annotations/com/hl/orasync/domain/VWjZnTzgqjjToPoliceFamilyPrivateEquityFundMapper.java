package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilyPrivateEquityFund;
import com.hl.archive.domain.entity.PoliceFamilyPrivateEquityFundToVWjZnTzgqjjMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__559;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__559.class,
    uses = {ConversionUtils.class,PoliceFamilyPrivateEquityFundToVWjZnTzgqjjMapper.class},
    imports = {}
)
public interface VWjZnTzgqjjToPoliceFamilyPrivateEquityFundMapper extends BaseMapper<VWjZnTzgqjj, PoliceFamilyPrivateEquityFund> {
  @Mapping(
      target = "fundNameCode",
      source = "mc"
  )
  @Mapping(
      target = "contractSigningDate",
      source = "qsrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "totalPaidAmount",
      source = "zje"
  )
  @Mapping(
      target = "subscriptionAmount",
      source = "rjje",
      qualifiedByName = {"strToBigDecimal"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "contractExpiryDate",
      source = "jzrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "name",
      source = "xmFr"
  )
  @Mapping(
      target = "fundInvestmentDirection",
      source = "jjtx"
  )
  @Mapping(
      target = "personalPaidAmount",
      source = "grje",
      qualifiedByName = {"strToBigDecimal"}
  )
  PoliceFamilyPrivateEquityFund convert(VWjZnTzgqjj source);

  @Mapping(
      target = "fundNameCode",
      source = "mc"
  )
  @Mapping(
      target = "contractSigningDate",
      source = "qsrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "totalPaidAmount",
      source = "zje"
  )
  @Mapping(
      target = "subscriptionAmount",
      source = "rjje",
      qualifiedByName = {"strToBigDecimal"}
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "contractExpiryDate",
      source = "jzrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "name",
      source = "xmFr"
  )
  @Mapping(
      target = "fundInvestmentDirection",
      source = "jjtx"
  )
  @Mapping(
      target = "personalPaidAmount",
      source = "grje",
      qualifiedByName = {"strToBigDecimal"}
  )
  PoliceFamilyPrivateEquityFund convert(VWjZnTzgqjj source,
      @MappingTarget PoliceFamilyPrivateEquityFund target);
}
