package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceHkMacauTaiwanPermit;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T16:15:23+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjBrGattxzToPoliceHkMacauTaiwanPermitMapperImpl implements VWjBrGattxzToPoliceHkMacauTaiwanPermitMapper {

    @Override
    public PoliceHkMacauTaiwanPermit convert(VWjBrGattxz source) {
        if ( source == null ) {
            return null;
        }

        PoliceHkMacauTaiwanPermit policeHkMacauTaiwanPermit = new PoliceHkMacauTaiwanPermit();

        policeHkMacauTaiwanPermit.setExpiryDate( ConversionUtils.strToDate( source.getYxqz() ) );
        policeHkMacauTaiwanPermit.setDocumentNumber( source.getZjhm() );
        policeHkMacauTaiwanPermit.setIdCard( source.getGmsfhm() );
        policeHkMacauTaiwanPermit.setDocumentName( source.getZjmc() );
        policeHkMacauTaiwanPermit.setIssueDate( ConversionUtils.strToDate( source.getQfrq() ) );
        policeHkMacauTaiwanPermit.setRemarks( source.getBz() );
        policeHkMacauTaiwanPermit.setCustodyOrganization( source.getBgjgmc() );

        return policeHkMacauTaiwanPermit;
    }

    @Override
    public PoliceHkMacauTaiwanPermit convert(VWjBrGattxz source, PoliceHkMacauTaiwanPermit target) {
        if ( source == null ) {
            return target;
        }

        target.setExpiryDate( ConversionUtils.strToDate( source.getYxqz() ) );
        target.setDocumentNumber( source.getZjhm() );
        target.setIdCard( source.getGmsfhm() );
        target.setDocumentName( source.getZjmc() );
        target.setIssueDate( ConversionUtils.strToDate( source.getQfrq() ) );
        target.setRemarks( source.getBz() );
        target.setCustodyOrganization( source.getBgjgmc() );

        return target;
    }
}
