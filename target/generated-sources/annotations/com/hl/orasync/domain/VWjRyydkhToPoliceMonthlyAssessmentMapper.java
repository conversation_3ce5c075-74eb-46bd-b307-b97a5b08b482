package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceMonthlyAssessment;
import com.hl.archive.domain.entity.PoliceMonthlyAssessmentToVWjRyydkhMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__559;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__559.class,
    uses = {ConversionUtils.class,PoliceMonthlyAssessmentToVWjRyydkhMapper.class},
    imports = {}
)
public interface VWjRyydkhToPoliceMonthlyAssessmentMapper extends BaseMapper<VWjRyydkh, PoliceMonthlyAssessment> {
  @Mapping(
      target = "assessmentNotice",
      source = "kptbpzmc"
  )
  @Mapping(
      target = "assessmentMonth",
      source = "yf"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "assessmentRanking",
      source = "pm",
      qualifiedByName = {"bigDecimalToString"}
  )
  @Mapping(
      target = "assessmentScore",
      source = "df",
      qualifiedByName = {"bigDecimalToString"}
  )
  @Mapping(
      target = "assessmentBonus",
      source = "jj",
      qualifiedByName = {"bigDecimalToString"}
  )
  @Mapping(
      target = "assessmentYear",
      source = "nd"
  )
  PoliceMonthlyAssessment convert(VWjRyydkh source);

  @Mapping(
      target = "assessmentNotice",
      source = "kptbpzmc"
  )
  @Mapping(
      target = "assessmentMonth",
      source = "yf"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "assessmentRanking",
      source = "pm",
      qualifiedByName = {"bigDecimalToString"}
  )
  @Mapping(
      target = "assessmentScore",
      source = "df",
      qualifiedByName = {"bigDecimalToString"}
  )
  @Mapping(
      target = "assessmentBonus",
      source = "jj",
      qualifiedByName = {"bigDecimalToString"}
  )
  @Mapping(
      target = "assessmentYear",
      source = "nd"
  )
  PoliceMonthlyAssessment convert(VWjRyydkh source, @MappingTarget PoliceMonthlyAssessment target);
}
