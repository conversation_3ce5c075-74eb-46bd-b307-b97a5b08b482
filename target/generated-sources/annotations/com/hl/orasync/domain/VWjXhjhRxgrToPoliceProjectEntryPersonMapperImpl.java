package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceProjectEntryPerson;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T16:15:23+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjXhjhRxgrToPoliceProjectEntryPersonMapperImpl implements VWjXhjhRxgrToPoliceProjectEntryPersonMapper {

    @Override
    public PoliceProjectEntryPerson convert(VWjXhjhRxgr source) {
        if ( source == null ) {
            return null;
        }

        PoliceProjectEntryPerson policeProjectEntryPerson = new PoliceProjectEntryPerson();

        policeProjectEntryPerson.setPoliceNumber( source.getJh() );
        policeProjectEntryPerson.setEntryTime( ConversionUtils.strToDate( source.getDjsj() ) );
        policeProjectEntryPerson.setUnit( source.getDwmc() );
        policeProjectEntryPerson.setHonorLevel( source.getRyjbmc() );
        policeProjectEntryPerson.setIdCard( source.getGmsfhm() );
        policeProjectEntryPerson.setHonorName( source.getZgrymc() );
        policeProjectEntryPerson.setName( source.getXm() );
        policeProjectEntryPerson.setAuditStatus( source.getShztmc() );
        policeProjectEntryPerson.setContactPerson( source.getPylxrXm() );
        policeProjectEntryPerson.setZjbh( source.getXxzjbh() );

        return policeProjectEntryPerson;
    }

    @Override
    public PoliceProjectEntryPerson convert(VWjXhjhRxgr source, PoliceProjectEntryPerson target) {
        if ( source == null ) {
            return target;
        }

        target.setPoliceNumber( source.getJh() );
        target.setEntryTime( ConversionUtils.strToDate( source.getDjsj() ) );
        target.setUnit( source.getDwmc() );
        target.setHonorLevel( source.getRyjbmc() );
        target.setIdCard( source.getGmsfhm() );
        target.setHonorName( source.getZgrymc() );
        target.setName( source.getXm() );
        target.setAuditStatus( source.getShztmc() );
        target.setContactPerson( source.getPylxrXm() );
        target.setZjbh( source.getXxzjbh() );

        return target;
    }
}
