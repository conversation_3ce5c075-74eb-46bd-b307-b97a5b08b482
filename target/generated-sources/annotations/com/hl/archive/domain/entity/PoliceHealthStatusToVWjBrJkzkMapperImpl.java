package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrJkzk;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T16:15:23+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceHealthStatusToVWjBrJkzkMapperImpl implements PoliceHealthStatusToVWjBrJkzkMapper {

    @Override
    public VWjBrJkzk convert(PoliceHealthStatus source) {
        if ( source == null ) {
            return null;
        }

        VWjBrJkzk vWjBrJkzk = new VWjBrJkzk();

        vWjBrJkzk.setGmsfhm( source.getIdCard() );
        vWjBrJkzk.setJbmc( source.getIllnessName() );
        if ( source.getDiagnosisDate() != null ) {
            vWjBrJkzk.setZysj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getDiagnosisDate() ) );
        }

        return vWjBrJkzk;
    }

    @Override
    public VWjBrJkzk convert(PoliceHealthStatus source, VWjBrJkzk target) {
        if ( source == null ) {
            return target;
        }

        target.setGmsfhm( source.getIdCard() );
        target.setJbmc( source.getIllnessName() );
        if ( source.getDiagnosisDate() != null ) {
            target.setZysj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getDiagnosisDate() ) );
        }
        else {
            target.setZysj( null );
        }

        return target;
    }
}
