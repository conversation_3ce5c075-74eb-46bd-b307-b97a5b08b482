package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyydkh;
import com.hl.orasync.domain.VWjRyydkhToPoliceMonthlyAssessmentMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__559;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__559.class,
    uses = {ConversionUtils.class,VWjRyydkhToPoliceMonthlyAssessmentMapper.class},
    imports = {}
)
public interface PoliceMonthlyAssessmentToVWjRyydkhMapper extends BaseMapper<PoliceMonthlyAssessment, VWjRyydkh> {
  @Mapping(
      target = "jj",
      source = "assessmentBonus"
  )
  @Mapping(
      target = "kptbpzmc",
      source = "assessmentNotice"
  )
  @Mapping(
      target = "df",
      source = "assessmentScore"
  )
  @Mapping(
      target = "nd",
      source = "assessmentYear"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "pm",
      source = "assessmentRanking"
  )
  @Mapping(
      target = "yf",
      source = "assessmentMonth"
  )
  VWjRyydkh convert(PoliceMonthlyAssessment source);

  @Mapping(
      target = "jj",
      source = "assessmentBonus"
  )
  @Mapping(
      target = "kptbpzmc",
      source = "assessmentNotice"
  )
  @Mapping(
      target = "df",
      source = "assessmentScore"
  )
  @Mapping(
      target = "nd",
      source = "assessmentYear"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "pm",
      source = "assessmentRanking"
  )
  @Mapping(
      target = "yf",
      source = "assessmentMonth"
  )
  VWjRyydkh convert(PoliceMonthlyAssessment source, @MappingTarget VWjRyydkh target);
}
