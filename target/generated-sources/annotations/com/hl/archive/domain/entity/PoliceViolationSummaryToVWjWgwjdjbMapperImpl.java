package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjWgwjdjb;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-09-18T16:15:23+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceViolationSummaryToVWjWgwjdjbMapperImpl implements PoliceViolationSummaryToVWjWgwjdjbMapper {

    @Override
    public VWjWgwjdjb convert(PoliceViolationSummary source) {
        if ( source == null ) {
            return null;
        }

        VWjWgwjdjb vWjWgwjdjb = new VWjWgwjdjb();

        vWjWgwjdjb.setAjbh( source.getCaseNo() );
        if ( source.getAcceptDate() != null ) {
            vWjWgwjdjb.setSlsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getAcceptDate() ) );
        }
        vWjWgwjdjb.setDjdwmc( source.getReportOrg() );
        vWjWgwjdjb.setLzscdw( source.getDetentionOrg() );
        if ( source.getDetentionDate() != null ) {
            vWjWgwjdjb.setLzscsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getDetentionDate() ) );
        }
        if ( source.getTransferDate() != null ) {
            vWjWgwjdjb.setYjsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getTransferDate() ) );
        }
        vWjWgwjdjb.setChqk( source.getPreliminaryResult() );
        if ( source.getPreliminaryStart() != null ) {
            vWjWgwjdjb.setChsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getPreliminaryStart() ) );
        }
        if ( source.getMeetingDate() != null ) {
            vWjWgwjdjb.setHssj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getMeetingDate() ) );
        }
        vWjWgwjdjb.setFxdw( source.getFoundOrg() );
        vWjWgwjdjb.setChdw( source.getPreliminaryOrg() );
        vWjWgwjdjb.setSldw( source.getAcceptOrg() );
        if ( source.getFoundDate() != null ) {
            vWjWgwjdjb.setFxsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getFoundDate() ) );
        }
        vWjWgwjdjb.setYjdw( source.getTransferOrg() );
        vWjWgwjdjb.setWjwfss( source.getViolationFact() );
        vWjWgwjdjb.setWjwflxmc( source.getViolationType() );
        vWjWgwjdjb.setHsdw( source.getMeetingOrg() );
        vWjWgwjdjb.setXxzjbh( source.getXxzjbh() );
        vWjWgwjdjb.setWtxslymc( source.getClueSource() );
        vWjWgwjdjb.setWtxsnr( source.getClueContent() );
        vWjWgwjdjb.setDcqk( source.getInvestigationResult() );
        if ( source.getInvestigationStart() != null ) {
            vWjWgwjdjb.setDcsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getInvestigationStart() ) );
        }
        vWjWgwjdjb.setHsjg( source.getMeetingResult() );
        vWjWgwjdjb.setLadw( source.getCaseOrg() );
        vWjWgwjdjb.setDcdw( source.getInvestigationOrg() );
        if ( source.getCaseDate() != null ) {
            vWjWgwjdjb.setLasj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getCaseDate() ) );
        }

        return vWjWgwjdjb;
    }

    @Override
    public VWjWgwjdjb convert(PoliceViolationSummary source, VWjWgwjdjb target) {
        if ( source == null ) {
            return target;
        }

        target.setAjbh( source.getCaseNo() );
        if ( source.getAcceptDate() != null ) {
            target.setSlsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getAcceptDate() ) );
        }
        else {
            target.setSlsj( null );
        }
        target.setDjdwmc( source.getReportOrg() );
        target.setLzscdw( source.getDetentionOrg() );
        if ( source.getDetentionDate() != null ) {
            target.setLzscsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getDetentionDate() ) );
        }
        else {
            target.setLzscsj( null );
        }
        if ( source.getTransferDate() != null ) {
            target.setYjsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getTransferDate() ) );
        }
        else {
            target.setYjsj( null );
        }
        target.setChqk( source.getPreliminaryResult() );
        if ( source.getPreliminaryStart() != null ) {
            target.setChsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getPreliminaryStart() ) );
        }
        else {
            target.setChsj( null );
        }
        if ( source.getMeetingDate() != null ) {
            target.setHssj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getMeetingDate() ) );
        }
        else {
            target.setHssj( null );
        }
        target.setFxdw( source.getFoundOrg() );
        target.setChdw( source.getPreliminaryOrg() );
        target.setSldw( source.getAcceptOrg() );
        if ( source.getFoundDate() != null ) {
            target.setFxsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getFoundDate() ) );
        }
        else {
            target.setFxsj( null );
        }
        target.setYjdw( source.getTransferOrg() );
        target.setWjwfss( source.getViolationFact() );
        target.setWjwflxmc( source.getViolationType() );
        target.setHsdw( source.getMeetingOrg() );
        target.setXxzjbh( source.getXxzjbh() );
        target.setWtxslymc( source.getClueSource() );
        target.setWtxsnr( source.getClueContent() );
        target.setDcqk( source.getInvestigationResult() );
        if ( source.getInvestigationStart() != null ) {
            target.setDcsj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getInvestigationStart() ) );
        }
        else {
            target.setDcsj( null );
        }
        target.setHsjg( source.getMeetingResult() );
        target.setLadw( source.getCaseOrg() );
        target.setDcdw( source.getInvestigationOrg() );
        if ( source.getCaseDate() != null ) {
            target.setLasj( DateTimeFormatter.ISO_LOCAL_DATE.format( source.getCaseDate() ) );
        }
        else {
            target.setLasj( null );
        }

        return target;
    }
}
